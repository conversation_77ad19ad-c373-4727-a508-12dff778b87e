"use client";

import { EVIEW_PORT } from "@/constants/enums";
import { TParamPageCommon } from "@/constants/types";
import ResetPasswordContainer from "../ResetPassword/ResetPasswordContainer";
import { AuthContainer } from "../components/AuthContainer";
import RightImageBlock from "../components/RightImageBlock";
import { useResetPassword } from "./useResetPassword";

const ResetPasswordWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleReset } = useResetPassword();
  return (
    <AuthContainer>
      <ResetPasswordContainer handleReset={handleReset} />
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </AuthContainer>
  );
};

export default ResetPasswordWrapper;

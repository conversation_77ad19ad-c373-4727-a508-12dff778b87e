import { NextRequest, NextResponse, userAgent } from "next/server";
import { EVIEW_PORT } from "./constants/enums";

const SEB_KEY = process.env.NEXT_PUBLIC_PREFIX_KEY_EXAM_PORTAL || "1000001";

export function middleware(request: NextRequest) {
  const url = request.nextUrl;
  const { device } = userAgent(request);
  console.log("device.type -->", device.type);
  const viewport =
    device.type === "mobile" ? EVIEW_PORT.MOBILE : EVIEW_PORT.DESKTOP;
  url.searchParams.set("viewport", viewport);

  const host = request.headers.get("host") || "";
  const userAgentData = request.headers.get("user-agent")?.toLowerCase() || "";

  // Detect SEB Domains
  const isSEBDomain = host.includes(SEB_KEY);

  // Detect SEB Browser
  const isSEBBrowser = userAgentData.includes("seb");

  // If accessing SEB domain but not using SEB browser, block access
  if (isSEBDomain && !isSEBBrowser) {
    return new NextResponse(
      `Access denied. Please use Safe Exam Browser. User-agent: ${userAgentData}`,
      {
        status: 403,
      },
    );
  }

  return NextResponse.next();
}

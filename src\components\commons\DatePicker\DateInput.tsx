import { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import dayjs from "dayjs";

import "react-datepicker/dist/react-datepicker.css";

interface DateInputProps {
  value?: string; // Accepts date as a string
  onChange: (formattedDate: string) => void;
}

const DateInput: React.FC<DateInputProps> = ({ value = "", onChange }) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  useEffect(() => {
    if (value) {
      const parsedDate = dayjs(value, "YYYY-MM-DD", true);
      if (parsedDate.isValid()) {
        setSelectedDate(parsedDate.toDate());
      } else {
        console.warn("Invalid date format");
      }
    }
  }, [value]);

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
    if (date) {
      const formattedDate = dayjs(date).format("YYYY-MM-DD");
      onChange(formattedDate);
    } else {
      onChange("");
    }
  };

  return (
    <div className="">
      <DatePicker
        selected={selectedDate}
        onChange={handleDateChange}
        dateFormat="yyyy-MM-dd"
        placeholderText="Select a date"
        className="!w-full rounded-lg border px-4 py-3 text-sm"
      />
    </div>
  );
};

export default DateInput;

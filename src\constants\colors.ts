// Use this one to define color name: https://chir.ag/projects/name-that-color

export enum EColor {
  TRANSPARENT = "transparent",
  MINE_SHAFT = "#333",
  MERCURY = "#E6E6E6",
  PANACHE = "#E5F4EA",
  CINNABAR = "#E83831",
  BURNT_SIENNA = "#ED605A",
  PROVINCIAL_PINK = "#FDEBEA",
  TARAWERA = "#093459",
  WHITE = "#FFFFFF",
  LYNCH = "#5B758A",
  FUN_GREEN = "#00802B",
  RED_COLOR = "#D3332D",
  WEB_ORANGE = "#FFAB00",
  CERULEAN = "#0591E2",
}

export enum BaseColor {
  PRIMARY = "#0A3150",
  DEFAULT = "#BB9E7F",
  SECONDARY = "#E83831",
  SUCCESS = "#009933",
  PRIMARY_50 = "#E7EAEE",
  PRIMARY_100 = "#B3BFC9",
  PRIMARY_200 = "#E9ECEF",
  PRIMARY_300 = "#5B758A",
  PRIMARY_400 = "#3B5A73",
  PRIMARY_700 = "#072339",
  DANGER = "#D3332D",
  WARNING = "#AA7200",
  GREY_100 = "#F0F2F4",
  GREY_700 = "#94979D",
  GREY_50 = "#F9FAFB",
}

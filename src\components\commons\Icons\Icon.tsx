import { CSSProperties, FC, MouseEvent } from "react";

import Image from "next/image";
import { EColor } from "@/constants/colors";
import { EIconSize, TypeIcon } from "./Icon.enums";
import { cn } from "@/lib/utils";

import { TIconProps } from "./Icon.d";

import "./Icon.scss";

const Icon: FC<TIconProps> = ({
  id,
  className,
  name,
  color = EColor.MINE_SHAFT,
  backgroundColor,
  size = EIconSize.MD,
  hoverStyle,
  activeIcon,
  disabledIcon,
  selectedIcon,
  isActive,
  isDisabled,
  isSelected,
  isStaticIcon = false,
  onClick,
  type = TypeIcon.DEFAULT,
}) => {
  if (type === TypeIcon.SVG) {
    return (
      <Image
        className={className}
        priority
        src={`/images/icons/${name}.svg`}
        alt={`icon-${name}`}
        height={size}
        width={size}
      />
    );
  }

  const { color: hoverColor, backgroundColor: hoverBackgroundColor } =
    hoverStyle ?? {
      backgroundColor: EColor.MERCURY,
    };

  const defaultColorStyle = {
    "--background-color": backgroundColor,
    "--background-hover-color": isStaticIcon
      ? backgroundColor
      : hoverBackgroundColor,
    "--icon-color": color,
    "--icon-hover-color": isStaticIcon ? color : hoverColor,
  } as CSSProperties;

  const finalIcon = (() => {
    const defaultIcon = { name };

    if (isDisabled) {
      const { color: disabledColor, backgroundColor: disabledBackgroundColor } =
        disabledIcon || {};

      const disabledColorStyle = {
        "--background-color": disabledBackgroundColor || EColor.TRANSPARENT,
        "--icon-color": disabledColor || EColor.MERCURY,
      } as CSSProperties;

      return { style: disabledColorStyle, ...defaultIcon, ...disabledIcon };
    }

    if (isSelected && selectedIcon) {
      const {
        color: selectedColor,
        backgroundColor: selectedBackgroundColor,
        ...rest
      } = selectedIcon;

      const selectedColorStyle = {
        "--background-color": selectedBackgroundColor,
        "--icon-color": selectedColor,
        "--background-hover-color": selectedBackgroundColor,
        "--icon-hover-color": selectedColor,
      } as CSSProperties;

      return { style: selectedColorStyle, ...rest };
    }

    if (isActive && activeIcon) {
      const { color: activeColor, ...rest } = activeIcon;

      const activeColorStyle = {
        "--background-color": EColor.TRANSPARENT,
        "--icon-color": activeColor ?? EColor.TARAWERA,
        "--icon-hover-color": activeColor ?? EColor.TARAWERA,
        "--background-hover-color": hoverBackgroundColor,
      } as CSSProperties;

      return { style: activeColorStyle, ...rest };
    }

    return { ...defaultIcon, style: defaultColorStyle };
  })();

  const handleOnClick = (e: MouseEvent<HTMLElement>) => {
    if (isDisabled) return;
    onClick && onClick(e);
  };

  return (
    <span
      id={id}
      style={
        { ...finalIcon.style, "--icon-size": `${size}px` } as CSSProperties
      }
      className={cn("icon", className, {
        "disabled pe-none": isDisabled,
        "cursor-pointer": Boolean(onClick) && !isDisabled,
      })}
      onClick={handleOnClick}
    >
      <i
        style={{ color: color, fontSize: `${size}px` }}
        className={cn(
          {
            uil: type === TypeIcon.LINE,
            uis: type === TypeIcon.DEFAULT,
          },
          `${finalIcon.name}`,
        )}
      ></i>
    </span>
  );
};

export default Icon;

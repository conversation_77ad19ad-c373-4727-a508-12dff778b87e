import { ExamCard } from "./exam";
import { baseApi } from "src/api/config/api";
import { APIResponse, Paginated } from "src/api/config/api.d";
import { MODULE_ROUTE, ManagementRoutes } from "src/api/config/routes";

export const getUpcomingExams = async (page: number, perPage: number) => {
  try {
    const response = await baseApi.get<APIResponse<Paginated<ExamCard[]>>>(
      ManagementRoutes[MODULE_ROUTE.STUDENT].UPCOMING,
      { params: { page, perPage } },
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching students:", error);
    throw error;
  }
};

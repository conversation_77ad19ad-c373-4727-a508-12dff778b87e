import React from "react";
import { BaseColor } from "@/constants/colors";
import { Image, Stack, Text } from "@chakra-ui/react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import Link from "next/link";

const ExamWelcome = () => {
  const user = useAuthStore((state) => state.user);

  return (
    <Stack>
      <Text
        fontWeight="bold"
        fontSize="32px"
        lineHeight="40px"
        color={BaseColor.PRIMARY}
        textTransform={"capitalize"}
      >
        welcome, {user ? user?.biography.first_name : "user"}
      </Text>

      <div className="flex flex-row gap-2">
        <Image src="/images/icons/centre.svg" alt="Miva Centre" />
        <Text fontSize="16px" fontWeight="medium">
          Miva flagship centre
        </Text>
        <Link
          href={"/exam/change-center"}
          className="ml-2 cursor-pointer rounded-sm border p-1 text-xs font-semibold text-[#0A3150]"
        >
          Request Centre Change
        </Link>
      </div>
    </Stack>
  );
};

export default ExamWelcome;

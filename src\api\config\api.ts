import { queryClient } from "@/components/providers/MainProvider/MainProvider";
import axios, { AxiosResponse } from "axios";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useRoleStore } from "src/store/RoleStore/role";

const TIMEOUT = 30000;

export const MIVA_EXAM_ENDPOINT =
  process.env.NEXT_PUBLIC_EXAM_SERVICE_ENDPOINT ||
  "https://ep-backend.staging.miva.university";

export const MIVA_SIS_ENDPOINT =
  process.env.NEXT_PUBLIC_SIS_SERVICE_ENDPOINT ||
  "https://sis-be.staging.miva.university";

const baseApi = axios.create({
  baseURL: MIVA_EXAM_ENDPOINT,
  timeout: TIMEOUT, // Set a timeout
  headers: {
    "Content-Type": "application/json",
    "x-origin-portal": "student",
  },
});

const interceptorsRequestOnFulfilled = async (config: any) => {
  // Modify the request config before sending
  // For example, you can add headers, authentication tokens, etc.
  const accessToken = useAuthStore.getState().access_token;

  const newConfig = { ...config };
  if (newConfig.headers && !newConfig.headers[`Authorization`]) {
    newConfig.headers[`Authorization`] = `Bearer ${accessToken}`;
  }
  return newConfig;
};

const interceptorsRequestOnRejected = async (error: any) => {
  // Handle request error
  return Promise.reject(error);
};

const interceptorsResponseOnFulfilled = async (
  response: AxiosResponse<AxiosResponse<any>>,
) => {
  return response;
};

const interceptorsResponseOnReject = async (error: any) => {
  if (error.response) {
    // The request was made, but the server responded with a status code
    // that falls out of the range of 2xx
    console.error("Response error:", error.response.data);
    console.error("Status code:", error.response.status);
    if (error.response.status == 401) {
      useAuthStore.setState((prev) => ({
        ...prev,
        access_token: "",
        user: undefined,
      }));
      useRoleStore.setState({
        assigned_role: undefined,
        permissions: [],
      });
      // Only redirect if we're not already on the login page
      if (
        window.location.pathname !== "/login" &&
        window.location.pathname !== "/"
      ) {
        window.location.href = "/login";
      }
    }

    if (error.response.status == 403) {
      // API returns forbidden error, refresh the current user and roles
      queryClient.invalidateQueries({
        queryKey: ["getCurrentUser"],
      });
    }
  } else if (error.request) {
    // The request was made, but no response was received
    console.error("No response received:", error.request);
  } else {
    console.error("Request setup error:", error.message);
  }

  return Promise.reject(error);
};

// Request interceptor
baseApi.interceptors.request.use(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  interceptorsRequestOnFulfilled,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  interceptorsRequestOnRejected,
);

// Response interceptor
baseApi.interceptors.response.use(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  interceptorsResponseOnFulfilled,
  interceptorsResponseOnReject,
);

// Without authentication and interceptors
// const authApi = axios.create({
//   baseURL: MIVA_SIS_ENDPOINT,
//   timeout: TIMEOUT, // Set a timeout
//   headers: {
//     "Content-Type": "application/json",
//     "x-origin-portal": "student",
//   },
// });

// Request interceptor
// authApi.interceptors.request.use(
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   interceptorsRequestOnFulfilled,
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   interceptorsRequestOnRejected,
// );

// Response interceptor
// authApi.interceptors.response.use(
//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   interceptorsResponseOnFulfilled,
//   interceptorsResponseOnReject,
// );

export { baseApi };

import { InputGroup, InputLeftAddon, Text } from "@chakra-ui/react";
import { ChangeEvent, useMemo } from "react";
import { IDateFieldProps } from "./DateInput.d";
import { Calendar } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./DateInput.scss";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";

const DatePickerInput = ({
  label,
  onChange,
  isInvalid = false,
  placeholder,
  fontSize,
  required,
  isDisabled,
  min,
  name,
  type = "date",
  size = "lg",
  ...props
}: IDateFieldProps) => {
  const format = `YYYY-MM-DD${type == "datetime" ? " HH:mm a" : ""}`;
  const selected = useMemo(() => {
    return props.value ? new Date(props.value) : undefined;
  }, [props.value]);

  const handleChange = (date?: Date | null) => {
    onChange?.({
      target: {
        value: date ? dayjs(date).format(format) : undefined,
        name,
      },
    } as ChangeEvent<HTMLInputElement>);
  };

  return (
    <div>
      {!!label && (
        <Text
          mb="8px"
          fontWeight="600"
          color="#0A3150"
          fontSize={fontSize || "14px"}
        >
          {label}{" "}
          {required && (
            <Text as="span" color="red.500">
              *
            </Text>
          )}
        </Text>
      )}
      <InputGroup size={size} isolation="initial">
        <InputLeftAddon bg={"#fff"}>
          <Calendar className="w-3.5" />
        </InputLeftAddon>
        <DatePicker
          value={props.value}
          selected={selected}
          onChange={(date) => {
            handleChange(date);
          }}
          showTimeInput={type === "datetime"}
          disabled={isDisabled}
          minDate={min ? new Date(min) : undefined}
          dateFormat={`yyyy-MM-dd${type == "datetime" ? " h:mm aa" : ""}`}
          wrapperClassName="w-full"
          calendarClassName="!border !border-[#e2e8f0] !rounded-md !shadow-sm"
          calendarIconClassName="bg-[#e2e8f0] border-[#e2e8f0]"
          popperClassName="!z-20"
          placeholderText={placeholder || format}
          className={cn(
            "text-small h-full min-h-10 w-full rounded-r-md border border-solid px-4 py-[13.5px] !font-primary focus:!border-[#3182ce] focus:!shadow-[0_0_1px_#3182ce] focus:outline-none focus:ring-0",
            {
              "!text-danger !border-danger": isInvalid,
            },
            {
              "cursor-not-allowed": isDisabled,
            },
            `text-[${fontSize || "14px"}]`,
            `py-[${size == "lg" ? "13.5px" : "11.75px"}]`,
          )}
          showMonthDropdown
          showYearDropdown
          dropdownMode="select"
          peekNextMonth
        />
      </InputGroup>
    </div>
  );
};

export default DatePickerInput;

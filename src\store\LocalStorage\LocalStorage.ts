import { useState, useEffect } from "react";

const useLocalStorage = (key: string, initialValue: any) => {
  // Initialize state with a function that doesn't access window
  const [storedValue, setStoredValue] = useState(initialValue);

  useEffect(() => {
    try {
      const item = window.localStorage.getItem(key);
      if (item) {
        setStoredValue(JSON.parse(item));
      }
    } catch (error) {
      console.error(error);
      return initialValue;
    }
  }, [key, initialValue]);

  const setValue = async (value: any) => {
    try {
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      await new Promise<void>((resolve) => {
        if (typeof window !== "undefined") {
          // Check if window exists
          window.localStorage.setItem(key, JSON.stringify(valueToStore));
        }
        resolve();
      });
    } catch (error) {
      console.log(error);
    }
  };

  return [storedValue, setValue];
};

export { useLocalStorage };

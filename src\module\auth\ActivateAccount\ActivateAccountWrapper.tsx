"use client";

import { Flex } from "@chakra-ui/react";

import { EVIEW_PORT } from "@/constants/enums";
import { TParamPageCommon } from "@/constants/types";
import ActivateAccountContainer from "./ActivateAccountContainer";
import RightImageBlock from "../components/RightImageBlock";

const ActivateAccountWrapper = ({ searchParams }: TParamPageCommon) => {
  const handleSubmitActivateAccount = async () => {
    // TODO add logic handle submit activate account
  };
  return (
    <Flex alignItems="center" gap={{ xl: "172px", md: "40px" }}>
      <ActivateAccountContainer handleSubmit={handleSubmitActivateAccount} />
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </Flex>
  );
};

export default ActivateAccountWrapper;

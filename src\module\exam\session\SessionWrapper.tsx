"use client";

import CommonDialog from "@/components/commons/CommonDialog/CommonDialog";
import { BaseColor } from "@/constants/colors";
import { Spacer, Spinner, Text, useToast, VStack } from "@chakra-ui/react";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState, useEffect, useRef } from "react";
import { useEndExam, useSubmitExam } from "./useExam";

import { EssayQuestion } from "./EssayQuestion";
import { NextAndPrevious } from "./NextAndPrevious";
import { ObjectiveOptions } from "./ObjectiveQuestion";
import { HelpPrompt } from "./HelpPrompt";
import { ExamTimerAndPagination } from "./ExamTimerAndPagination";
// import { EncryptedData, Encryption } from "@/utils/encryption";
import { useProctoring } from "src/providers/proctor/ProctoringContext";
import { IStartSessionParams, PossibleViolation } from "miva-proctor-sdk";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import "quill-editor-math/dist/index.css";
import { useGetAllQuestions } from "@/hooks/useExamSession/useGetAllQuestions";
import { useGetRemainingExamTime } from "@/hooks/useExamSession/useGetRemainingTime";
import { useGetSingleQuestionDetails } from "@/hooks/useExamSession/useGetSingleQuestionDetails";
import { useQueryClient } from "@tanstack/react-query";
import { browserUtils } from "@/utils/browserUtils";
import SessionSnackbar from "./components/SessionSnackbar";
import { debounce } from "lodash";
import { QUESTION_TYPES } from "@/constants/commons";
import ViewEditor from "@/components/commons/Editor/ViewEditor";

type ExamQuestion = {
  id: string;
  question: string; // HTML text
  question_type: "ESSAY" | "MULTIPLE_CHOICE";
  word_count?: number; // For ESSAY
  options?: { id: string; value: string }[];
  // ----- Additional fields for user's local answer tracking -----
  answered?: boolean; // True if user typed or picked an answer
  userAnswer?: string; // For essay or for display
  userOptionId?: string; // For multiple choice
  userOptionIndex?: number; // For highlighting
  isSubmitted?: boolean;
  lastSubmittedAnswer?: string;
};

const ExamSessionWrapper = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  const isUserEditing = useRef(false);
  const hasStoppedProctoring = useRef(false);

  // Remove encryption for SEB browser
  // const encryptedExamId = searchParams.get("exam_id");
  // const encryptedSessionId = searchParams.get("session_id");
  const examId = searchParams.get("exam_id");
  const sessionId = searchParams.get("session_id");
  // const pageParam = searchParams.get("page");
  // const limitParam = searchParams.get("limit");
  const course_id = searchParams.get("course_id");

  // const [examId, setExamId] = useState<string | null>(null);
  // const [sessionId, setSessionId] = useState<string | null>(null);

  const [examQuestions, setExamQuestions] = useState<ExamQuestion[]>([]);
  const [currentPage, setCurrentPage] = useState(1);

  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  const [showSubmitConfirmationDialog, setShowSubmitConfirmationDialog] =
    useState(false);
  const [showOfflineDialog, setShowOfflineDialog] = useState(false);
  const [showIntegrityWatchDialog, setShowIntegrityWatchDialog] =
    useState(false);

  const {
    onStartProctoring,
    onStopProctoring,
    isProctoring,
    onViolation,
    removeViolationListener,
    proctorManagerRef,
    sessionId: proctorSessionId,
  } = useProctoring();

  const [violationData, setViolationData] = useState({
    violationType: "",
    violationCount: 0,
  });

  const [, setLastSavedTime] = useState<string | null>(
    localStorage.getItem("lastSavedTime") || null,
  );
  const [pagesWithAnswers, setPagesWithAnswers] = useState<number[]>([]);

  const toast = useToast();
  const { user } = useAuthStore();

  useEffect(() => {
    // Memoize violation types for faster comparison
    const VIOLATION_TYPES = new Set([
      PossibleViolation.SwitchedFocus,
      PossibleViolation.MultipleFaces,
      PossibleViolation.NoFace,
      PossibleViolation.AudioNoise,
      PossibleViolation.StoppedScreenSharing,
      PossibleViolation.ToggledFullscreen,
    ]);

    // Debounce the violation handler to prevent rapid firing
    const debouncedViolationHandler = debounce(
      async (violation: PossibleViolation) => {
        if (!proctorSessionId) return;

        try {
          const data = await proctorManagerRef.current?.getActivityMetrics({
            sessionId: proctorSessionId,
            activityType: violation,
          });

          if (data?.status) {
            setViolationData({
              violationType: data.data.activityType,
              violationCount:
                data.data.metricsCount === 0 ? 1 : data.data.metricsCount,
            });
          }
        } catch (error) {
          console.error("Error handling violation:", error);
        }
      },
      500,
    );

    // Set up violation listener with optimized checks
    onViolation(async (violation: PossibleViolation) => {
      if (VIOLATION_TYPES.has(violation)) {
        setShowIntegrityWatchDialog(true);
        debouncedViolationHandler(violation);
      }
    });

    return () => {
      removeViolationListener();
      debouncedViolationHandler.cancel(); // Clean up debounced function
    };
  }, [onViolation, removeViolationListener, proctorSessionId]);

  const {
    data: questionsData,
    refetch: refetchQuestions,
    isLoading: fetchQuestionsIsLoading,
    isSuccess: fetchQuestionsIsSuccess,
  } = useGetAllQuestions({
    exam_id: examId ?? "",
    session_id: sessionId ?? "",
    // limit: Number(limitParam) || 10,
    // page: Number(pageParam) || 1,
  });
  const {
    data: remainingExamTime,
    isLoading: getRemainingExamTimIsLoading,
    refetch: refetchRemainingTime,
  } = useGetRemainingExamTime({
    exam_id: examId ?? "",
  });

  const submitExamMutation = useSubmitExam();
  const endExamMutation = useEndExam();

  useEffect(() => {
    if (!examId || hasStoppedProctoring.current) return;

    // Only start proctoring if there is remaining time
    if (remainingExamTime && remainingExamTime?.remaining_time > 0) {
      const startSessionParams: IStartSessionParams = {
        courseId: course_id || "",
        examId: examId || "",
        studentId: user?.id || "",
        studentEmail: user?.contact_information?.email || "",
        timestamp: new Date().toISOString(),
      };

      onStartProctoring(startSessionParams).then((success) => {
        if (!success) {
          // If permissions weren't granted, redirect back
          if (browserUtils.isSEBBrowser()) {
            router.push(`/exam/details?exam_id=${examId}`);
          } else {
            router.push(`/exam/details/${examId}`);
          }
          toast({
            description:
              "Proctoring permissions are required to take this exam",
            status: "error",
          });
        }
      });
    }
  }, [user, examId, course_id, remainingExamTime]);

  useEffect(() => {
    if (!examId || !sessionId) return;

    const storedData = localStorage.getItem("exam_questions");
    if (storedData) {
      try {
        const parsed: ExamQuestion[] = JSON.parse(storedData);
        setExamQuestions(parsed);
      } catch (err) {
        console.error("Could not parse local exam_questions:", err);
      }
    } else {
      refetchQuestions();
    }
  }, [examId, sessionId]);

  useEffect(() => {
    if (fetchQuestionsIsSuccess && questionsData?.length) {
      let questionWithFlags = [];
      if (examQuestions.length === 0) {
        questionWithFlags = questionsData.map((q: any) => ({
          ...q,
          answered: q.answered || false,
          userAnswer: q.userAnswer || "",
          userOptionId: q.userOptionId || "",
          userOptionIndex: q.userOptionIndex ?? undefined,
        }));

        setExamQuestions(questionWithFlags);

        localStorage.setItem(
          "exam_questions",
          JSON.stringify(questionWithFlags),
        );
      } else {
        questionWithFlags = examQuestions.map((q: any) => ({
          ...q,
          answered: q.answered || false,
          userAnswer: q.userAnswer || "",
          userOptionId: q.userOptionId || "",
          userOptionIndex: q.userOptionIndex ?? undefined,
        }));
      }

      const initiallyAnsweredPages = questionWithFlags
        .map((q: ExamQuestion, idx: number) => (q.answered ? idx + 1 : -1))
        .filter((idx: number) => idx !== -1);

      setPagesWithAnswers(initiallyAnsweredPages);
    }
  }, [examQuestions, fetchQuestionsIsSuccess, questionsData]);

  const totalPages = questionsData?.length ?? examQuestions.length ?? 1;

  const currentQuestion = examQuestions[currentPage - 1] || null;

  const { data: singleQuestionData, isLoading: fetchSingleQuestionIsLoading } =
    useGetSingleQuestionDetails({
      exam_id: examId as string,
      session_id: sessionId as string,
      question_id: currentQuestion?.id,
    });

  useEffect(() => {
    if (
      !singleQuestionData ||
      fetchSingleQuestionIsLoading ||
      isUserEditing.current
    ) {
      return;
    }

    setExamQuestions((prevQuestions) => {
      const updatedQuestions = [...prevQuestions];
      const idx = currentPage - 1;

      if (updatedQuestions[idx]?.question_type === "ESSAY") {
        if (
          singleQuestionData.answer_text !== undefined &&
          singleQuestionData.answer_text?.trim().length > 0 &&
          (updatedQuestions[idx]?.userAnswer === undefined ||
            updatedQuestions[idx]?.userAnswer?.trim().length === 0)
        ) {
          updatedQuestions[idx] = {
            ...updatedQuestions[idx],
            userAnswer: singleQuestionData.answer_text,
          };
        }
      } else if (updatedQuestions[idx]?.question_type === "MULTIPLE_CHOICE") {
        const selectedOptionIndex = updatedQuestions[idx].options?.findIndex(
          (opt) => opt.id === singleQuestionData.selected_option_id,
        );

        if (selectedOptionIndex !== undefined && selectedOptionIndex >= 0) {
          updatedQuestions[idx] = {
            ...updatedQuestions[idx],
            userOptionId: singleQuestionData.selected_option_id,
            userOptionIndex: selectedOptionIndex,
          };
        }
      }
      return updatedQuestions;
    });
  }, [singleQuestionData, fetchSingleQuestionIsLoading, currentPage]);

  const shouldSubmitAnswer = (pageIndex: number): boolean => {
    const question = examQuestions[pageIndex];
    if (!question || !question.id) return false;

    const currentAnswer =
      question.question_type === "ESSAY"
        ? question.userAnswer?.trim()
        : question.userOptionId;

    if (!currentAnswer) return false;

    return (
      !question.isSubmitted || question.lastSubmittedAnswer !== currentAnswer
    );
  };

  const handleSubmitSingleExamAnswer = (pageIndex: number) => {
    const question = examQuestions[pageIndex];
    if (!question || !question.id) return;

    const currentAnswer =
      question.question_type === "ESSAY"
        ? question.userAnswer?.trim()
        : question.userOptionId;

    if (!currentAnswer) return;

    if (
      question.isSubmitted &&
      question.lastSubmittedAnswer === currentAnswer
    ) {
      return;
    }

    const answerPayload = {
      question_id: question.id,
      ...(question.question_type === "ESSAY"
        ? { answer_text: currentAnswer }
        : { option_id: currentAnswer }),
    };

    submitExamMutation.mutate(
      {
        exam_id: examId as string,
        session_id: sessionId as string,
        answers: [answerPayload],
      },
      {
        onSuccess: (response) => {
          if (response?.data?.status === "OK") {
            const now = new Date().toISOString();
            localStorage.setItem("lastSavedTime", now);
            setLastSavedTime(now);

            setExamQuestions((prev) => {
              const updated = [...prev];
              updated[pageIndex] = {
                ...updated[pageIndex],
                isSubmitted: true,
                lastSubmittedAnswer: currentAnswer,
              };
              localStorage.setItem("exam_questions", JSON.stringify(updated));
              return updated;
            });
          } else {
            toast({
              description: "Unable to submit answer",
              status: "error",
            });
          }
        },
        onError: (error) => {
          console.error("Error submitting single question:", error);
        },
      },
    );
  };

  const onEssayAnswerUpdated = (val: string) => {
    isUserEditing.current = true;
    setExamQuestions((prev) => {
      const updated = [...prev];
      updated[currentPage - 1] = {
        ...updated[currentPage - 1],
        userAnswer: val,
        answered: val.trim().length > 0,
        isSubmitted: false,
      };
      localStorage.setItem("exam_questions", JSON.stringify(updated));
      return updated;
    });

    if (!pagesWithAnswers.includes(currentPage) && val.trim().length > 0) {
      setPagesWithAnswers([...pagesWithAnswers, currentPage]);
    }
  };

  const onSelectQuestionOption = (optionIndex: number) => {
    if (!currentQuestion || !currentQuestion.options) return;

    const picked = currentQuestion.options[optionIndex];

    const newQuestions = [...examQuestions];
    const idx = currentPage - 1;
    newQuestions[idx] = {
      ...newQuestions[idx],
      answered: true,
      userAnswer: picked.value,
      userOptionId: picked.id,
      userOptionIndex: optionIndex,
      isSubmitted: false,
    };

    setExamQuestions(newQuestions);
    localStorage.setItem("exam_questions", JSON.stringify(newQuestions));

    if (!pagesWithAnswers.includes(currentPage)) {
      setPagesWithAnswers([...pagesWithAnswers, currentPage]);
    }
  };

  const onNext = () => {
    if (currentPage < totalPages) {
      const currentIndex = currentPage - 1;
      if (shouldSubmitAnswer(currentIndex)) {
        handleSubmitSingleExamAnswer(currentIndex);
      }
      setCurrentPage((prev) => prev + 1);
    }
  };

  const onPrevious = () => {
    if (currentPage > 1) {
      const currentIndex = currentPage - 1;
      if (shouldSubmitAnswer(currentIndex)) {
        handleSubmitSingleExamAnswer(currentIndex);
      }
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleSubmitAllAnswers = () => {
    const payloadAnswers = examQuestions.map((q) => {
      const ans: {
        question_id: string;
        answer_text?: string;
        option_id?: string;
      } = {
        question_id: q.id,
      };
      if (q.question_type === "ESSAY") {
        ans.answer_text = q.userAnswer || "";
      } else if (q.question_type === "MULTIPLE_CHOICE") {
        ans.option_id = q.userOptionId || "";
      }
      return ans;
    });

    submitExamMutation.mutate(
      {
        exam_id: examId as string,
        session_id: sessionId as string,
        answers: payloadAnswers,
      },
      {
        onSuccess: (response) => {
          if (response.data.status === "OK") {
            localStorage.removeItem("exam_questions");
            localStorage.removeItem("lastSavedTime");
          }
        },
        onError: (error) => {
          console.error("Error in handleSubmitAllAnswers:", error);
        },
      },
    );
  };

  const onSubmitClicked = () => {
    if (checkUnansweredQuestions()) {
      setShowSubmitConfirmationDialog(true);
    } else {
      setShowSubmitDialog(true);
    }
  };

  const onConfirmSubmit = () => {
    handleSubmitAllAnswers();
    if (isProctoring) {
      onStopProctoring();
    }
    endExamMutation.mutate(
      {
        exam_id: examId as string,
        session_id: sessionId as string,
      },
      {
        onSuccess: (response) => {
          if (response.data.status === "OK") {
            setShowSubmitDialog(false);
            router.replace("/exam/submitted");
            toast({
              description: response?.data?.message,
              status: "success",
            });
          } else {
            toast({
              description: response?.data?.message,
              status: "error",
            });
          }
        },
        onError: (error) => {
          console.error(error);
          toast({
            description:
              "Unable to end the exam – it looks like it might have already been submitted.",
            status: "error",
          });
        },
      },
    );
  };

  const onCancelSubmit = () => {
    setShowSubmitConfirmationDialog(false);
    setShowSubmitDialog(false);
  };

  const checkUnansweredQuestions = () => {
    const unanswered = examQuestions.filter((q) => !q.answered);

    return unanswered.length > 0;
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (currentQuestion && currentQuestion.answered) {
        const currentIndex = currentPage - 1;
        handleSubmitSingleExamAnswer(currentIndex);
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [currentQuestion]);

  useEffect(() => {
    let checkInterval: NodeJS.Timeout | null = null;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    let networkStatusInterval: NodeJS.Timeout | null = null;
    // check if there is an actual internet connection
    const checkInternet = async () => {
      try {
        await fetch("https://www.google.com/favicon.ico", {
          method: "HEAD",
          mode: "no-cors",
        });
        setShowOfflineDialog(false);
        // Clear interval if we successfully connect
        if (checkInterval) {
          clearInterval(checkInterval);
          checkInterval = null;
        }
      } catch (error) {
        setShowOfflineDialog(true);
      }
    };

    const handleNetworkChange = () => {
      if (navigator.onLine) {
        // If browser reports online, clear interval and dialog
        setShowOfflineDialog(false);
        if (checkInterval) {
          clearInterval(checkInterval);
          checkInterval = null;
        }
      } else {
        // If browser reports offline, start checking
        setShowOfflineDialog(true);
        checkInternet();
        checkInterval = setInterval(checkInternet, 15000);
      }
    };

    const startNetworkPolling = () => {
      networkStatusInterval = setInterval(() => {
        if (!navigator.onLine) {
          setShowOfflineDialog(true);
          if (!checkInterval) {
            checkInterval = setInterval(checkInternet, 5000);
          }
        } else {
          checkInternet();
        }
      }, 5000);
    };

    handleNetworkChange();
    checkInternet();
    startNetworkPolling();

    window.addEventListener("online", handleNetworkChange);
    window.addEventListener("offline", handleNetworkChange);

    return () => {
      window.removeEventListener("online", handleNetworkChange);
      window.removeEventListener("offline", handleNetworkChange);
      if (isProctoring) {
        onStopProctoring();
      }
      if (checkInterval) clearInterval(checkInterval);
      if (networkStatusInterval) clearInterval(networkStatusInterval);
    };
  }, []);

  const handleOfflineDialogClose = () => {
    setShowOfflineDialog(false);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setLastSavedTime(localStorage.getItem("lastSavedTime"));
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (!sessionId && examId) {
      router.push(`/exam/details/${examId}`);
    }
  }, [sessionId, examId, router]);

  useEffect(() => {
    const cleanupExamSession = () => {
      if (isProctoring) {
        onStopProctoring();
      }
      localStorage.removeItem("exam_questions");
    };

    window.addEventListener("beforeunload", cleanupExamSession);
    window.addEventListener("pagehide", cleanupExamSession);
    window.addEventListener("unload", cleanupExamSession);

    return () => {
      window.removeEventListener("beforeunload", cleanupExamSession);
      window.removeEventListener("pagehide", cleanupExamSession);
      window.addEventListener("unload", cleanupExamSession);
      cleanupExamSession();
    };
  }, [isProctoring]);

  useEffect(() => {
    if (remainingExamTime?.remaining_time === 0) {
      onConfirmSubmit();
    }
  }, [remainingExamTime?.remaining_time]);

  useEffect(() => {
    const interval = setInterval(() => {
      refetchRemainingTime();
    }, 60000);
    return () => clearInterval(interval);
  }, [refetchRemainingTime]);

  const renderEssayQuestion = () => {
    if (!currentQuestion) return null;

    return (
      <EssayQuestion
        key={currentQuestion.id}
        answerText={currentQuestion.userAnswer || ""}
        onAnswerUpdate={onEssayAnswerUpdated}
        numberOfAcceptedWord={currentQuestion.word_count || 500}
        exam_id={examId as string}
        question_id={currentQuestion.id}
        session_id={sessionId as string}
        options={currentQuestion.options || []}
      />
    );
  };

  const renderMultipleChoiceQuestion = () => {
    if (!currentQuestion) return null;

    return (
      <ObjectiveOptions
        key={currentQuestion.id}
        options={currentQuestion.options?.map((opt) => opt.value) || []}
        onSelectOption={onSelectQuestionOption}
        selectedOption={currentQuestion.userOptionIndex}
        exam_id={examId as string}
        question_id={currentQuestion.id}
        session_id={sessionId as string}
      />
    );
  };

  const renderQuestionByType = () => {
    if (!currentQuestion) return null;

    switch (currentQuestion.question_type) {
      case QUESTION_TYPES.ESSAY:
        return renderEssayQuestion();
      case QUESTION_TYPES.MULTIPLE_CHOICE:
        return renderMultipleChoiceQuestion();
      default:
        return null;
    }
  };

  return (
    <div className="mx-auto flex h-full flex-col gap-6 pb-10">
      <ExamTimerAndPagination
        pagesWithAnswers={pagesWithAnswers}
        currentPage={currentPage}
        onGoToPage={(pageNum) => {
          const currentIndex = currentPage - 1;
          if (shouldSubmitAnswer(currentIndex)) {
            handleSubmitSingleExamAnswer(currentIndex);
          }
          setCurrentPage(pageNum);
        }}
        onSubmit={onSubmitClicked}
        countdownSeconds={remainingExamTime?.remaining_time || 0}
        totalPages={totalPages}
      />

      {fetchQuestionsIsLoading || getRemainingExamTimIsLoading ? (
        <div className="flex w-full flex-col items-center justify-center">
          <Spinner />
        </div>
      ) : (
        <VStack
          className="h-full w-full max-w-[1200px] px-4"
          mx="auto"
          my="auto"
          align="stretch"
        >
          <Text
            fontWeight="bold"
            fontSize="24px"
            color={BaseColor.PRIMARY}
            className="text-center"
          >
            Question {currentPage}
          </Text>
          <div className="flex min-h-[100px] w-full items-center justify-center rounded-lg bg-white">
            {currentQuestion && <ViewEditor value={currentQuestion.question} />}
          </div>

          {renderQuestionByType()}

          <NextAndPrevious
            page={currentPage}
            max={totalPages}
            onNext={onNext}
            onPrevious={onPrevious}
            loading={submitExamMutation.isPending}
          />
          <Spacer />
          <HelpPrompt />
        </VStack>
      )}

      {/* Submit Dialog */}
      <CommonDialog
        open={showSubmitDialog}
        header={<Text>Submit Exam</Text>}
        confirmText="SUBMIT"
        cancelText="NO, REVISIT QUESTIONS"
        handleProceed={onConfirmSubmit}
        handleCancel={onCancelSubmit}
        isFooter={true}
        maxWidth={500}
        confirmLoading={endExamMutation.isPending}
      >
        <Text fontSize="14px" fontWeight="medium" textAlign="center">
          Are you sure you want to submit this exam?
        </Text>
      </CommonDialog>

      {/* Confirmation Dialog */}
      <CommonDialog
        open={showSubmitConfirmationDialog}
        header={<Text>Submit Exam</Text>}
        confirmText="SUBMIT"
        cancelText="NO, REVISIT QUESTIONS"
        handleProceed={onConfirmSubmit}
        handleCancel={onCancelSubmit}
        isFooter={true}
        maxWidth={500}
        confirmLoading={endExamMutation.isPending}
      >
        <Text fontSize="14px" fontWeight="medium" textAlign="center">
          <b>You have unanswered questions!</b> Are you sure you want to submit
          your exam, or would you like to review them first?
        </Text>
      </CommonDialog>

      {/* Offline Dialog */}
      <CommonDialog
        open={showOfflineDialog}
        handleCancel={handleOfflineDialogClose}
        variant="danger"
        header={
          <Text
            fontSize="14px"
            fontWeight="medium"
            textAlign="center"
            className="max-w-[700px] text-white"
          >
            Oops! You&apos;re Offline
          </Text>
        }
        isFooter={false}
      >
        <Text fontSize="14px" fontWeight="medium" textAlign="center">
          Your internet connection is currently unstable. We&apos;ve saved your
          answers so far. Please reconnect to resume your exam.
        </Text>
      </CommonDialog>

      {/* Integrity Watch */}
      <SessionSnackbar
        violationData={violationData}
        setShowIntegrityWatchDialog={setShowIntegrityWatchDialog}
        showIntegrityWatchDialog={showIntegrityWatchDialog}
      />
      {/* <CommonDialog
        open={showIntegrityWatchDialog}
        handleCancel={() => setShowIntegrityWatchDialog(false)}
        variant="danger"
        header={
          <Text
            fontSize="14px"
            fontWeight="medium"
            textAlign="center"
            className="max-w-[700px] text-white"
          >
            Exam Integrity Watch
          </Text>
        }
        isFooter={false}
      >
        <div className="flex flex-col items-center justify-center gap-6">
          <Text fontSize="14px" fontWeight="medium" textAlign="center">
            {`The counter for below tracks the number of potential test infractions
            detected of ${violationData.violationType} during your session.`}
          </Text>
          <Text fontSize="40px" fontWeight="bold" textAlign="center"></Text>
        </div>
      </CommonDialog> */}
    </div>
  );
};

export default ExamSessionWrapper;

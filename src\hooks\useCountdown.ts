import { useState, useEffect, useCallback } from "react";

type CountdownProps = {
  hours?: number;
  minutes?: number;
  seconds?: number;
};

export const useCountdown = ({
  hours = 0,
  minutes = 0,
  seconds = 0,
}: CountdownProps) => {
  const calculateTotalSeconds = (h: number, m: number, s: number) =>
    h * 3600 + m * 60 + s;

  const [totalSeconds, setTotalSeconds] = useState<number>(
    calculateTotalSeconds(hours, minutes, seconds),
  );
  const [paused, setPaused] = useState(false);
  const [over, setOver] = useState(false);

  const h = Math.floor(totalSeconds / 3600);
  const remainderAfterHours = totalSeconds % 3600;
  const m = Math.floor(remainderAfterHours / 60);
  const sLeft = remainderAfterHours % 60;

  const tick = useCallback(() => {
    if (paused || over) return;
    setTotalSeconds((prev) => {
      if (prev <= 0) {
        setOver(true);
        return 0;
      }
      return prev - 1;
    });
  }, [paused, over]);

  useEffect(() => {
    const timerId = setInterval(tick, 1000);
    return () => clearInterval(timerId);
  }, [tick]);

  useEffect(() => {
    const newTotal = calculateTotalSeconds(hours, minutes, seconds);
    setTotalSeconds(newTotal);
    setPaused(false);
    setOver(false);
  }, [hours, minutes, seconds]);

  const pause = () => setPaused(true);
  const resume = () => setPaused(false);
  const reset = () => {
    const newTotal = calculateTotalSeconds(hours, minutes, seconds);
    setTotalSeconds(newTotal);
    setPaused(false);
    setOver(false);
  };

  return {
    hoursLeft: h < 10 ? `0${h}` : h,
    minutesLeft: m < 10 ? `0${m}` : m,
    secondsLeft: sLeft < 10 ? `0${sLeft}` : sLeft,
    isOver: over,
    pause,
    resume,
    reset,
  };
};

import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { useState } from "react";
import { baseApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { IForgotPasswordValue } from "./ForgotPassword";

export const useForgotPassword = () => {
  const toast = useToast();
  const [verifyEmail, setVerifyEmail] = useState<string>();

  const handleInitiateReset = async ({ email }: IForgotPasswordValue) => {
    try {
      await baseApi.post<APIResponse<string>>(
        Routes[MODULE_ROUTE.AUTH].REQUEST_RESET,
        { email },
      );
      setVerifyEmail(email);
      toast({
        description: "We have just sent you an email with reset instructions",
        status: "success",
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
      console.warn(error);
    }
  };

  const handleResend = async () => {
    try {
      await baseApi.post<APIResponse<string>>(
        Routes[MODULE_ROUTE.AUTH].REQUEST_RESET,
        { email: verifyEmail },
      );
      toast({
        description: "We have just sent you an email with reset instructions",
        status: "success",
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
      console.warn(error);
    }
  };

  return {
    handleInitiateReset,
    verifyEmail,
    handleResend,
  };
};

import { format } from "date-fns";
import dayjs from "dayjs";
import { browserUtils } from "./browserUtils";

export const FORMAT_DATE_FULL_DAY = "yyyy-MM-dd";
export const FORMAT_TIME_AMPM = "hh:mm a";

export const dateRangeOptions = [
  {
    label: "Today",
    value: "today",
  },
  {
    label: "Yesterday",
    value: "yesterday",
  },
  {
    label: "This Week",
    value: "this_week",
  },
  {
    label: "Last Week",
    value: "last_week",
  },
  {
    label: "This Month",
    value: "this_month",
  },
  {
    label: "Last Month",
    value: "last_month",
  },
  {
    label: "This Year",
    value: "this_year",
  },
  {
    label: "Last Year",
    value: "last_year",
  },
];

export const getPastYears = (
  startYear: number = 2020,
): { label: string; value: string }[] => {
  const currentYear = new Date().getFullYear();
  const years: { label: string; value: string }[] = [];

  for (let year = currentYear; year >= startYear; year--) {
    years.push({ label: year.toString(), value: year.toString() });
  }

  return years;
};

export const updateDateRange = (rangeType: string) => {
  const today = dayjs();

  switch (rangeType) {
    case "today":
      return {
        rangeFrom: today.format("YYYY-MM-DD"),
        rangeTo: today.format("YYYY-MM-DD"),
      };

    case "yesterday":
      return {
        rangeFrom: today.subtract(1, "day").format("YYYY-MM-DD"),
        rangeTo: today.subtract(1, "day").format("YYYY-MM-DD"),
      };

    case "this_week":
      return {
        rangeFrom: today.startOf("week").format("YYYY-MM-DD"),
        rangeTo: today.endOf("week").format("YYYY-MM-DD"),
      };

    case "last_week":
      return {
        rangeFrom: today
          .subtract(1, "week")
          .startOf("week")
          .format("YYYY-MM-DD"),
        rangeTo: today.subtract(1, "week").endOf("week").format("YYYY-MM-DD"),
      };

    case "this_month":
      return {
        rangeFrom: today.startOf("month").format("YYYY-MM-DD"),
        rangeTo: today.endOf("month").format("YYYY-MM-DD"),
      };

    case "last_month":
      return {
        rangeFrom: today
          .subtract(1, "month")
          .startOf("month")
          .format("YYYY-MM-DD"),
        rangeTo: today.subtract(1, "month").endOf("month").format("YYYY-MM-DD"),
      };

    case "this_year":
      return {
        rangeFrom: today.startOf("year").format("YYYY-MM-DD"),
        rangeTo: today.endOf("year").format("YYYY-MM-DD"),
      };

    case "last_year":
      return {
        rangeFrom: today
          .subtract(1, "year")
          .startOf("year")
          .format("YYYY-MM-DD"),
        rangeTo: today.subtract(1, "year").endOf("year").format("YYYY-MM-DD"),
      };

    default:
      return { rangeFrom: "", rangeTo: "" };
  }
};

export function formatDate(isoDate: string): string {
  const date = new Date(isoDate);

  const day = date.getUTCDate();
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];
  const month = monthNames[date.getUTCMonth()];
  const year = date.getUTCFullYear();

  // Function to get ordinal suffix (st, nd, rd, th)
  const getOrdinalSuffix = (n: number): string => {
    if (n > 3 && n < 21) return "th"; // 4th - 20th
    switch (n % 10) {
      case 1:
        return "st";
      case 2:
        return "nd";
      case 3:
        return "rd";
      default:
        return "th";
    }
  };

  return `${day}${getOrdinalSuffix(day)} ${month}. ${year}`;
}

export function formatTime(isoString: string): string {
  if (!isoString) return "Invalid date";
  const time = isoString.split(/[T\s]+/)[1].split(":");
  if (parseInt(time[0]) > 12) {
    time[0] = (parseInt(time[0]) - 12).toString();
    time[2] = "PM";
  } else if (parseInt(time[0]) === 12) {
    time[2] = "PM";
  } else {
    time[2] = "AM";
  }
  return `${time[0]}:${time[1]} ${time[2]}`;
}

export function formatTime2(isoString: string): string {
  if (!isoString) return "Invalid date";
  const time = isoString.split(" ")[1].split(":");
  if (parseInt(time[0]) > 12) {
    time[0] = (parseInt(time[0]) - 12).toString();
    time[2] = "PM";
  } else if (parseInt(time[0]) === 12) {
    time[2] = "PM";
  } else {
    time[2] = "AM";
  }
  return `${time[0]}:${time[1]} ${time[2]}`;
}
export function addOneHour(isoString: string) {
  const date = new Date(isoString); // Convert the ISO string to a Date object
  const userOffset = date.getTimezoneOffset(); // Get the user's timezone offset in minutes
  date.setMinutes(date.getMinutes() + userOffset); // Adjust the time to user's local time
  date.setHours(date.getHours() + 1); // Add 1 hour to the date
  date.setMinutes(date.getMinutes() - userOffset); // Convert back to UTC
  return date.toISOString(); // Convert the updated Date object back to an ISO string
}

// Convert UTC time to local client time
// Input format: 2025-02-21 09:47:00.858 +0000 +0000
// Input format: 2025-02-21T09:47:00Z
export function convertUTCTimeToLocalTime(utcTimeString: string) {
  // Create a Date object from the UTC time string
  const utcDate = new Date(utcTimeString);
  // Convert to local client time
  const localDateString = utcDate.toLocaleString();
  // Format the local date string
  return format(new Date(localDateString), FORMAT_DATE_FULL_DAY);
}

export function convertUTCTimeToLocalTimeV2(
  utcTimeString: string,
  isTimeFormat?: boolean,
) {
  if (browserUtils.isSEBBrowser()) {
    return convertUTCTimeForSEB(utcTimeString, isTimeFormat);
  }
  const utcDate = new Date(utcTimeString);

  // Format directly using date-fns
  return format(
    utcDate,
    isTimeFormat ? FORMAT_TIME_AMPM : FORMAT_DATE_FULL_DAY,
  );
}

// Alternative method specifically for SEB browser
export function convertUTCTimeForSEB(
  dateString: string,
  isTimeFormat?: boolean,
) {
  try {
    let year: number,
      month: number,
      day: number,
      hours: number,
      minutes: number,
      seconds: number;
    // Parse the date string format: "2025-03-14 16:30:00 +0000 +0000"
    // Check if the date string contains 'T' (ISO format) or space (current format)
    if (dateString.includes("T")) {
      // Handle ISO format: "2025-03-20T03:00:00Z"
      const [datePart, timePart] = dateString.split("T");
      [year, month, day] = datePart.split("-").map(Number);
      [hours, minutes, seconds] = timePart
        .replace("Z", "")
        .split(":")
        .map(Number);
    } else {
      // Handle current format: "2025-03-14 16:30:00 +0000 +0000"
      const [datePart, timePart] = dateString.split(" ");
      [year, month, day] = datePart.split("-").map(Number);
      [hours, minutes, seconds] = timePart.split(":").map(Number);
    }

    const utcDate = new Date(
      Date.UTC(year, month - 1, day, hours, minutes, seconds),
    );

    // Get client's current timezone
    const clientTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Define format options for output
    const formatOptions: Intl.DateTimeFormatOptions = isTimeFormat
      ? {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
          timeZone: clientTimeZone,
        }
      : {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour12: true,
          timeZone: clientTimeZone,
        };

    const formatter = new Intl.DateTimeFormat("en-US", formatOptions);
    let formattedTime = formatter.format(utcDate);

    if (isTimeFormat) {
      formattedTime = formattedTime
        .replace(",", "")
        .replace(/\s*([AP]M)/, " $1")
        .replace(":", ".");

      formattedTime = formattedTime.replace(/^0/, "");
      return formattedTime;
    }

    return formattedTime;
  } catch (error) {
    console.error("SEB date conversion error:", error, "for date:", dateString);
    return dateString;
  }
}

{"name": "miva-exam-portal", "version": "0.1.0", "description": "Next.js boilerplate with husky, lint-staged, eslint + prettier, jest, react-testing-library, storybook", "private": true, "scripts": {"dev": "next dev", "dev:network": "next dev -H 0.0.0.0 -p 3000", "build": "next build", "start": "next start", "prettier:format": "prettier --write \"src/**/*.{ts,tsx,json}\"", "prettier:check": "prettier --check \"src/**/*.{ts,tsx,json}\"", "lint": "next lint", "eslint:format": "eslint src --fix", "test": "jest test", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "test:ci": "jest --ci", "test:e2e-api-ci": "yarn playwright test tests/e2e/api/specs/ --project chromium", "test:e2e-ui-ci": "CI=true yarn playwright test tests/e2e/ui/specs/ --project chromium", "test:report": "playwright show-report", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "postinstall": "husky", "generate": "yarn plop --plopfile ./.plop/plopfile.js"}, "engines": {"node": ">=18.20.2"}, "packageManager": "yarn@1.22.22", "dependencies": {"@aarkue/tiptap-math-extension": "^1.3.6", "@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@t3-oss/env-nextjs": "0.10.1", "@tanstack/react-query": "^5.56.2", "@tanstack/react-table": "^8.20.1", "@tanstack/react-virtual": "^3.11.1", "@tiptap/core": "^2.14.0", "@tiptap/extension-blockquote": "^2.12.0", "@tiptap/extension-code-block": "^2.12.0", "@tiptap/extension-color": "^2.12.0", "@tiptap/extension-heading": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-list-item": "^2.12.0", "@tiptap/extension-ordered-list": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-strike": "^2.12.0", "@tiptap/extension-subscript": "^2.12.0", "@tiptap/extension-superscript": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/crypto-js": "^4.2.2", "@types/react-katex": "^3.0.4", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "eslint-config-airbnb-typescript": "17.1.0", "file-saver": "^2.0.5", "formik": "^2.4.6", "framer-motion": "^11.2.10", "katex": "^0.16.22", "lodash": "^4.17.21", "lucide-react": "^0.427.0", "miva-proctor-sdk": "0.0.58", "next": "14.2.3", "pretty-quick": "^4.0.0", "quill-editor-math": "^0.2.0", "react": "18.3.1", "react-datepicker": "^7.5.0", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-infinite-scroll-component": "^6.1.0", "react-is": "^18.3.1", "react-katex": "^3.1.0", "recharts": "^2.12.7", "sass": "^1.77.6", "string-width": "^7.1.0", "strip-ansi": "^7.1.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.3", "validator": "^13.12.0", "yarn": "^1.22.22", "yup": "^1.4.0", "zod": "3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@playwright/test": "^1.48.2", "@storybook/addon-essentials": "^8.1.1", "@storybook/addon-interactions": "^8.1.4", "@storybook/addon-links": "^8.1.5", "@storybook/blocks": "^8.1.3", "@storybook/nextjs": "^8.1.5", "@storybook/react": "^8.1.1", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "6.4.5", "@testing-library/react": "15.0.7", "@testing-library/user-event": "14.5.2", "@types/file-saver": "^2.0.7", "@types/jest": "29.5.12", "@types/node": "^20.14.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/validator": "^13.12.0", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "autoprefixer": "10.4.18", "babel-jest": "29.7.0", "eslint": "8.57.0", "eslint-config-next": "14.2.3", "eslint-config-prettier": "9.1.0", "eslint-plugin-import-helpers": "1.3.1", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-react": "7.34.1", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-storybook": "0.8.0", "eslint-plugin-testing-library": "6.2.2", "husky": "9.0.11", "identity-obj-proxy": "3.0.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-watch-typeahead": "2.2.2", "lint-staged": "15.2.5", "pinst": "3.0.0", "playwright-ctrf-json-reporter": "^0.0.18", "plop": "4.0.1", "postcss": "8.4.38", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "0.6.0", "storybook": "^8.1.4", "tailwind-scrollbar": "3.1.0", "tailwindcss": "3.4.3", "typescript": "5.4.2"}, "lint-staged": {"src/**/*.{ts,tsx}": ["pretty-quick --staged", "eslint --fix", "prettier --write"]}, "resolutions": {"strip-ansi": "7.1.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}
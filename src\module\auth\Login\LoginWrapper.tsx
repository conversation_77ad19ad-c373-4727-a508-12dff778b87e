"use client";

import { EVIEW_PORT } from "@/constants/enums";
import { Routers } from "@/constants/routers";
import { TParamPageCommon } from "@/constants/types";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuthStore } from "../../../store/AuthenticationStore/authentication";
import { AuthContainer } from "../components/AuthContainer";
import RightImageBlock from "../components/RightImageBlock";
import LoginFormContainer from "./LoginContainer";
import LoginVerifyFormContainer from "./LoginVerifyContainer";
import { useLogin } from "./useLogin";

const LoginWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleLogin, handleLoginVerify, verifyEmail } = useLogin();
  const accessToken = useAuthStore((state) => state.access_token);
  const user = useAuthStore((state) => state.user);

  const router = useRouter();

  useEffect(() => {
    if (accessToken && user) {
      router.replace(Routers.examManagement);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [accessToken]);

  return (
    <AuthContainer>
      {verifyEmail ? (
        <LoginVerifyFormContainer handleLoginVerify={handleLoginVerify} />
      ) : (
        <LoginFormContainer handleLogin={handleLogin} />
      )}
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </AuthContainer>
  );
};

export default LoginWrapper;

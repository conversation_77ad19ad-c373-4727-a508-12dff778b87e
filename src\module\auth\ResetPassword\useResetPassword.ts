/* eslint-disable react-hooks/exhaustive-deps */
import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { baseApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { IResetPasswordValue } from "./ResetPassword.d";

export const useResetPassword = () => {
  const router = useRouter();
  const toast = useToast();

  const searchParams = useSearchParams();
  const email = decodeURI(searchParams.get("email") || "")
    .trim()
    .replace(/ /g, "+");

  useEffect(() => {
    if (!email) {
      router.replace("/forgot-password");
    }
  }, [email]);

  const handleReset = async ({
    password,
    confirm_password,
    reset_token,
  }: IResetPasswordValue) => {
    try {
      await baseApi.post<APIResponse<string>>(Routes[MODULE_ROUTE.AUTH].RESET, {
        email,
        password,
        confirm_password,
        reset_token,
      });
      toast({
        description: "Password reset successful",
        status: "success",
      });
      router.replace("/login");
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
      console.warn(error);
    }
  };

  return {
    handleReset,
  };
};

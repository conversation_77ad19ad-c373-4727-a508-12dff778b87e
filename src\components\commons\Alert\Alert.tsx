/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  AlertProps,
  Box,
  Alert as <PERSON>kra<PERSON><PERSON><PERSON>,
  CloseButton,
  Flex,
  useDisclosure,
  Text,
} from "@chakra-ui/react";

export const Alert = ({
  dismissable,
  children,
  defaultIsOpen = true,
  ...props
}: AlertProps & { dismissable?: boolean; defaultIsOpen?: boolean }) => {
  const {
    isOpen: isVisible,
    onClose,
    onOpen,
  } = useDisclosure({ defaultIsOpen });

  return isVisible ? (
    <ChakraAlert {...props} alignItems="center" mb={4} rounded={8}>
      <Flex alignItems="center" flexGrow={1} fontSize="14px">
        {children}
      </Flex>
      {dismissable && (
        <CloseButton
          alignSelf="flex-start"
          position="relative"
          right={-1}
          top={-1}
          onClick={onClose}
        />
      )}
    </ChakraAlert>
  ) : (
    <div></div>
  );
};

import React, { createContext, useContext } from "react";
import {
  Evidence,
  IActivityMetricResponse,
  IGetActivityMetricsParams,
  IStartSessionParams,
  PossibleViolation,
  ProctoringManager,
  TrackingSession,
  ViolationsTracker,
} from "miva-proctor-sdk";
import { APIResponse } from "@/api/config/api.d";

export type ViolationCallback = (
  violation: PossibleViolation,
  evidence?: Evidence,
) => void;

export interface ProctoringOptions {
  onPossibleViolation: (
    violation: PossibleViolation,
    evidence?: Evidence,
  ) => void;
  onRandomPhoto: (photo: Blob) => void;
}

export interface ProctoringContextType {
  proctorManagerRef: React.MutableRefObject<ProctoringManager | null>;
  trackingSessionRef: React.MutableRefObject<TrackingSession | null>;
  violationsTrackerRef: React.MutableRefObject<ViolationsTracker | null>;
  initProctor: (studentId: string, studentEmail: string) => void;
  initTrackingSession: () => void;
  initViolationsTracker: () => void;
  onStartProctoring: (
    startSessionParams: IStartSessionParams,
  ) => Promise<boolean>;
  onStopProctoring: () => Promise<void>;
  isProctoring: boolean;
  getActivityMetrics: (
    params: IGetActivityMetricsParams,
  ) => Promise<APIResponse<IActivityMetricResponse> | undefined>;
  onViolation: (callback: ViolationCallback) => void;
  removeViolationListener: () => void;
  sessionId: string | null;
}

export const ProctoringContext = createContext<
  ProctoringContextType | undefined
>(undefined);

export const useProctoring = (): ProctoringContextType => {
  const context = useContext(ProctoringContext);
  if (!context) {
    throw new Error("useProctoring must be used within a ProctoringProvider");
  }
  return context;
};

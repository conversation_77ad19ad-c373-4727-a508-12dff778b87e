import React, { <PERSON> } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";

import { IWarningPopupProps } from "./WarningPopup.d";
import { EIconName, EIconSize } from "@/components/commons/Icons/Icon.enums";
import { EColor } from "@/constants/colors";
import Icon from "@/components/commons/Icons/Icon";

const WarningPopup: FC<IWarningPopupProps> = ({
  open,
  header,
  children,
  handleCancel,
  handleProceed,
  cancelText,
  confirmText,
}) => {
  return (
    <Dialog open={open}>
      <DialogContent className="top-[20%] p-[16px]" isCloseIcon={false}>
        <DialogHeader className="rounded-md bg-primary p-2 text-white">
          <DialogTitle className="flex items-center justify-between text-[18px] font-semibold text-white">
            <div>{header}</div>
            <Icon
              onClick={handleCancel}
              size={EIconSize.SM}
              isStaticIcon={true}
              name={EIconName.CLOSE}
              color={EColor.WHITE}
            />
          </DialogTitle>
        </DialogHeader>
        <div className="text-[14px] font-medium text-primary">{children}</div>
        <DialogFooter className="sm:justify-end">
          <ButtonCTA
            onClick={handleCancel}
            type="button"
            variant={EButtonType.OUTLINE}
          >
            {cancelText || "Cancel"}
          </ButtonCTA>
          <ButtonCTA onClick={handleProceed} type="button">
            {confirmText || "Proceed"}
          </ButtonCTA>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WarningPopup;

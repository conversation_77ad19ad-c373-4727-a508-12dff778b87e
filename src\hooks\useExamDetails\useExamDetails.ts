import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { getExamDetails } from "@/api/repository/exam-details/get-exam-details";
import { ExamCard } from "@/module/exam/management/ExamManagement";

export const useExamDetails = (id: string) => {
  const query = useQuery({
    queryKey: ["getExamDetails"],
    queryFn: () => getExamDetails(id),
    enabled: !!id,
    retry: 1,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const data = response.data as ExamCard;
      return data;
    },
    staleTime: 0,
    refetchOnMount: true,
  });
  return {
    ...query,
    isFetching: query.isFetching && query.isLoading,
  };
};

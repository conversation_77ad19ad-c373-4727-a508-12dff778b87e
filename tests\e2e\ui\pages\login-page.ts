import { type Locator, type Page, expect } from "@playwright/test";
import { getVerificationCode } from "tests/e2e/utils/email-utils";

class LoginPage {
  readonly page: Page;
  readonly emailField: Locator;
  readonly passwordField: Locator;
  readonly loginButton: Locator;
  readonly verificationCodeField: Locator;
  readonly verifyButton: Locator;
  readonly notificationIcon: Locator;
  readonly toast: Locator;
  readonly errorMsgToast: Locator;
  readonly loginPageTitle: Locator;

  constructor(page: Page) {
    this.page = page;
    this.emailField = page.getByPlaceholder("Email Address");
    this.passwordField = page.getByPlaceholder("Password");
    this.loginButton = page.getByRole("button", { name: "Login" });
    this.verificationCodeField = page.getByPlaceholder("Code");
    this.verifyButton = page.getByRole("button", { name: "Verify" });
    this.notificationIcon = page.getByRole("button", { name: "<PERSON>u" });
    this.toast = page.locator("[id='toast-1']");
    this.errorMsgToast = page.locator("[id='toast-2-description']");
    this.loginPageTitle = page.getByText("Log In");
  }

  private async fillEmail(email: string) {
    await this.emailField.fill(email);
  }

  private async fillPassword(password: string) {
    await this.passwordField.fill(password);
  }

  private async fillVerificationCode(verificationCode: string) {
    await this.verificationCodeField.fill(verificationCode);
  }

  async doLogin(email: string, password: string) {
    await this.fillEmail(email);
    await this.fillPassword(password);
    await this.loginButton.click();
    await this.page.waitForTimeout(6000);
    const verificationCode = await getVerificationCode(email, "loginRequest");
    await this.fillVerificationCode(verificationCode);
    await this.verifyButton.click();
  }

  async checkLoggedIn() {
    await expect(this.page).toHaveURL(/dashboard/);
    await expect(this.notificationIcon).toBeVisible();
  }

  async doInvalidEmailLogin(password: string) {
    await this.fillEmail("<EMAIL>");
    await this.fillPassword(password);
    await this.loginButton.click();
  }

  async doInvalidPasswordLogin(email: string) {
    await this.fillEmail(email);
    await this.fillPassword("rdf2454352Q@");
    await this.loginButton.click();
  }

  async assertLoginPageVisible() {
    await expect(this.loginPageTitle).toBeVisible();
  }
  async assertInvalidCredentialMessage() {
    await expect(this.page).toHaveURL(/login/);
    await expect(this.toast).toBeVisible();
    // await this.page.waitForTimeout(1000);
    // const errorMsg = this.errorMsgToast;
    // await expect(errorMsg).toContainText(
    //   "Incorrect email or password",
    // );
  }
}
export default LoginPage;

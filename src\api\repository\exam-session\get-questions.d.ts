export interface IGetQuestionsParam {
  exam_id: string;
  session_id: string;
  page?: number;
  limit?: number;
}

export interface ExamQuestionsRequestType {
  exam_id: string;
  session_id: string;
  page?: number;
  limit?: number;
}
export interface ExamQuestionsRemainitimeRequestType {
  exam_id: string;
}
export interface StartExamRequestType {
  exam_id: string;
}
export interface ExamQuestionResponseType {
  status: string;
  message: string;
  data: SingleQuestionType[];
}

export interface SingleQuestionType {
  created_at: string;
  id: string;
  options: Option[];
  points: number;
  question: string;
  question_type: string;
  updated_at: string;
  word_count: number;
}

export interface Option {
  id: string;
  value: string;
}

export interface GetSingleQuestionRequestType {
  exam_id: string;
  session_id: string;
  question_id: string;
}

import { test } from "@playwright/test";
import LoginPage from "../pages/login-page";
import uiPages from "../../utils/ui-pages";
import DashboardPage from "../pages/dashboard-page";

const email = process.env.EMAIL!;
const password = process.env.PASSWORD!;
let loginPage: LoginPage;
let dashboardPage: DashboardPage;

test("Admin can logout", { tag: ["@smoke", "@positive"] }, async ({ page }) => {
  loginPage = new LoginPage(page);
  dashboardPage = new DashboardPage(page);

  await page.goto(uiPages.dashboard);
  await dashboardPage.logout();
  await loginPage.assertLoginPageVisible();
});

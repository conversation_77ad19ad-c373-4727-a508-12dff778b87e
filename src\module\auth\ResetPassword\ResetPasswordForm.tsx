import { Box, Flex, Stack } from "@chakra-ui/react";
import { FormikProps } from "formik";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";
import HeaderForm from "../components/HeaderForm";

import { InputError } from "@/components/commons/InputField/InputError";
import { BaseColor } from "@/constants/colors";
import { IResetPasswordValue } from "./ResetPassword.d";
import { useState } from "react";
import { EyeOpenIcon } from "@radix-ui/react-icons";

const ResetPasswordForm = (props: FormikProps<IResetPasswordValue>) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const {
    confirm_password: confirmPasswordTouched,
    password: passwordTouched,
    reset_token: resetTouched,
  } = touched;
  const {
    confirm_password: confirmPasswordError,
    password: passwordError,
    reset_token: resetError,
  } = errors;

  const togglePasswordView = (type: "password" | "confirmPassword") => {
    if (type === "password") setIsPasswordVisible(!isPasswordVisible);
    if (type === "confirmPassword")
      setIsConfirmPasswordVisible(!isConfirmPasswordVisible);
  };
  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Set new password"
        subTitle="Create a password to activate your account"
      />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Enter reset token"
            borderColor={BaseColor.PRIMARY_400}
            color="white"
            _placeholder={{ color: BaseColor.PRIMARY_400 }}
            type="text"
            name="reset_token"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.reset_token}
          />
          <InputError error={resetError} touched={resetTouched} />
        </Box>
        <Box>
          <div className="relative">
            <InputField
              placeholder="Create your password"
              borderColor={BaseColor.PRIMARY_400}
              color="white"
              _placeholder={{ color: BaseColor.PRIMARY_400 }}
              type={isPasswordVisible ? "text" : "password"}
              name="password"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.password}
            />
            <span className="absolute right-3 top-4 z-[9999] !cursor-pointer">
              <EyeOpenIcon
                className="h-4 w-4 text-white"
                onClick={() => togglePasswordView("password")}
              />
            </span>
          </div>
          <InputError error={passwordError} touched={passwordTouched} />
        </Box>
        <Box>
          <div className="relative">
            <InputField
              placeholder="Confirm your password"
              borderColor={BaseColor.PRIMARY_400}
              color="white"
              _placeholder={{ color: BaseColor.PRIMARY_400 }}
              type={isConfirmPasswordVisible ? "text" : "password"}
              name="confirm_password"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.confirm_password}
            />
            <span className="absolute right-3 top-4 z-[9999] !cursor-pointer">
              <EyeOpenIcon
                className="h-4 w-4 text-white"
                onClick={() => togglePasswordView("confirmPassword")}
              />
            </span>
          </div>
          <InputError
            error={confirmPasswordError}
            touched={confirmPasswordTouched}
          />
        </Box>
      </Stack>
      <Stack mt="48px">
        <ButtonCTA onClick={() => handleSubmit()} isLoading={isSubmitting}>
          Reset password
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ResetPasswordForm;

import playwright, { APIRequestContext } from "playwright";
import { executeRequest } from "./api-requests-utils";
import apiMethods from "./api-methods";

async function initializeRequestContext() {
  return playwright.request.newContext({
    baseURL: "https://api.mail.tm",
  });
}

export async function getEmailAuthToken(email: string) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      address: email,
      password: "testing1",
    },
  };

  try {
    const response = await executeRequest(
      apiContext,
      "/token",
      apiMethods.post,
      requestOptions,
    );

    if (!response.ok) {
      console.error(`Failed to fetch token: ${response.status()}`);
      return null;
    }

    const body = await response.json();
    return body.token;
  } catch (error) {
    console.error("Error getting email auth token:", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

export async function geInboxes(email: string, authToken: string) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      Authorization: `Bearer ${authToken}`,
    },
  };
  try {
    const response = await executeRequest(
      apiContext,
      "/messages",
      apiMethods.get,
      requestOptions,
    );

    if (!response.ok()) {
      console.error(`Failed to fetch inboxes: ${response.status()}`);
      return null;
    }
    const body = await response.json();
    return body;
  } catch (error) {
    console.error("Error fetching inboxes:", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

export async function fetchMessageById(messageId: string, authToken: string) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      Authorization: `Bearer ${authToken}`,
    },
  };
  try {
    const response = await executeRequest(
      apiContext,
      `/messages/${messageId}`,
      apiMethods.get,
      requestOptions,
    );

    if (!response.ok()) {
      console.error(`Failed to fetch message: ${response.status()}`);
      return null;
    }
    const body = await response.json();
    return body;
  } catch (error) {
    console.error("Error fetching message:", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

export async function getVerificationCode(
  email: string,
  emailRequestType: string,
) {
  const authToken = await getEmailAuthToken(email);
  if (!authToken) {
    throw new Error("Authentication failed: Unable to retrieve token.");
  }
  const inboxes = await geInboxes(email, authToken);

  const newestMessageId = inboxes?.["hydra:member"]?.[0]?.id;
  if (!newestMessageId) {
    console.error("message ID not found.");
    return null;
  }
  const newestMessageDetails = await fetchMessageById(
    newestMessageId,
    authToken,
  );

  const emailIntro = inboxes?.["hydra:member"]?.[0]?.intro;
  if (!emailIntro) {
    console.error("Intro text not found in email body.");
    return null;
  }

  let verificationCode;

  switch (emailRequestType) {
    case "loginRequest":
      verificationCode = newestMessageDetails?.text
        ?.split(".")?.[0]
        ?.split(":")?.[1]
        .trim();
      break;
    case "resetPasswordRequest":
      verificationCode = newestMessageDetails?.text
        ?.split(".")?.[1]
        ?.split(":")?.[1]
        .trim()
        ?.split("\n")?.[0];
      break;
    default:
      throw new Error(`Invalid Email request type: ${emailRequestType}`);
  }
  return verificationCode;
}

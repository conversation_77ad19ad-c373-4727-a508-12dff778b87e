import React, { useEffect, useState } from "react";
import ExamCards from "./ExamCards";
import { TabPanel } from "@chakra-ui/react";
import { ExamCard } from "../ExamManagement";
import { useGetUpcomingExams } from "@/hooks/useExamManagement/useGetUpcomingExams";
import { Paginated } from "@/api/config/api.d";

const UpcomingExams = () => {
  const [upcomingExams, setUpcomingExams] = useState<Paginated<ExamCard[]>>({
    currentPage: 1,
    data: [],
    nextPage: 1,
    perPage: 20,
    prevPage: 0,
    total: 0,
    totalPages: 0,
  });
  const { isLoading, data, refetch, isFetching } = useGetUpcomingExams(
    upcomingExams.nextPage,
    upcomingExams.perPage,
  );
  useEffect(() => {
    if (data && !isFetching) {
      setUpcomingExams((prev) => ({
        ...data.meta,
        data: [...prev.data, ...data.examWithTag],
      }));
    }
  }, [isFetching]);

  return (
    <TabPanel>
      <ExamCards
        data={upcomingExams.data}
        fetch={refetch}
        hasMore={data?.meta.currentPage !== data?.meta.nextPage}
        loading={isLoading}
      />
    </TabPanel>
  );
};

export default UpcomingExams;

"use client";

import { Card } from "@/components/ui/card";
import React from "react";
import "quill-editor-math/dist/index.css";
import ViewEditor from "@/components/commons/Editor/ViewEditor";

type ObjectiveOptionProps = {
  letter: string;
  value: string;
  selected: boolean;
  onClick: () => void;
};

export const ObjectiveOption = ({
  letter,
  value,
  selected,
  onClick,
}: ObjectiveOptionProps) => {
  const extraCardClasses = selected ? `bg-[#0A3150]` : "";
  const textColorClass = selected ? "text-white" : "text-[#0A3150]";

  return (
    <Card
      className={`flex w-full cursor-pointer flex-row items-center px-4 py-3 ${extraCardClasses}`}
      onClick={onClick}
    >
      <div
        className={`m-2 w-[40px] ${textColorClass}`}
        dangerouslySetInnerHTML={{ __html: letter }}
      />
      <div
        className={`max-[1200px] flex max-h-[100px] w-full rounded-lg ${textColorClass}`}
      >
        <ViewEditor value={value} />
      </div>
    </Card>
  );
};

import { BaseColor } from "@/constants/colors";
import { Box } from "@chakra-ui/react";
import Image from "next/image";

const ExamLiveTag = () => {
  return (
    <Box
      borderRadius="8px"
      bg={BaseColor.SECONDARY}
      className="flex flex-row items-center justify-center gap-2 text-white"
      py="8px"
      px="16px"
      fontWeight="semibold"
      fontSize="14px"
    >
      <Image alt="live tag" src="/images/icons/dot.svg" width={6} height={6} />
      Live
    </Box>
  );
};

export default ExamLiveTag;

"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BaseColor } from "@/constants/colors";
import { HStack, Image, Text, VStack } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

const prompts = [
  "Please turn your face to the left",
  "Please turn your face to the right",
  "Please turn your face to the front",
];

const VerifyIdentityWrapper = () => {
  const [promptIndex, setPromptIndex] = useState(0);
  const [verificationComplete, setVerificationComplete] = useState(false);
  const router = useRouter();

  const onClickNext = () => {
    if (promptIndex >= prompts.length - 1) {
      setVerificationComplete(true);
      return;
    }
    setPromptIndex(promptIndex + 1);
  };

  const onClickStartExam = () => {
    router.push("/exam/session");
  };

  return (
    <div className="mx-auto mt-[40px] flex h-full flex-col gap-6 px-[100px] pb-10">
      <HStack cursor="pointer" mb="30px">
        <Image alt="arrow-back" src="/images/icons/arrow-back.svg" />
      </HStack>

      {!verificationComplete && (
        <VStack
          maxWidth="366px"
          mx="auto"
          alignItems="center"
          justifyContent="center"
        >
          <Text
            fontWeight="bold"
            fontSize="32px"
            color={BaseColor.PRIMARY}
            textAlign="center"
            pb="60px"
          >
            Verify your identity
          </Text>

          <Image
            alt="webcam-preview"
            src="/images/webcam-preview.svg"
            pb="48px"
          />

          <Text
            fontWeight="bold"
            fontSize="24px"
            color={BaseColor.PRIMARY}
            textAlign="center"
          >
            Instructions
          </Text>

          <Text
            fontWeight="medium"
            fontSize="16px"
            color={BaseColor.PRIMARY}
            textAlign="center"
            mb="20px"
          >
            {prompts[promptIndex]}
          </Text>

          <Button className="w-[366px] px-10 py-6" onClick={onClickNext}>
            NEXT
          </Button>
        </VStack>
      )}

      {verificationComplete && (
        <VStack
          maxWidth="366px"
          mx="auto"
          alignItems="center"
          justifyContent="center"
        >
          <Text
            fontWeight="bold"
            fontSize="32px"
            color={BaseColor.PRIMARY}
            textAlign="center"
            pb="60px"
          >
            Verification Complete
          </Text>

          <Image
            alt="complete-mock-image"
            src="/images/exam-verification-complete-mock-image.png"
          />

          <Text fontSize="24px" fontWeight="bold" color={BaseColor.PRIMARY}>
            Olabisi Olaniran
          </Text>
          <Text
            fontSize="18px"
            fontWeight="bold"
            color={BaseColor.PRIMARY_400}
            mb="20px"
          >
            30000696
          </Text>

          <Image
            alt="check-circle"
            src="/images/icons/check-circle.svg"
            mb="36px"
          />

          <Button className="w-[366px] px-10 py-6" onClick={onClickStartExam}>
            Start Exam
          </Button>
        </VStack>
      )}
    </div>
  );
};

export default VerifyIdentityWrapper;

import React, { useRef, useState, useEffect, ReactNode } from "react";

interface Props {
  fetching: boolean;
  next: () => void;
  hasMore: boolean;
  children: ReactNode;
  length: number;
  fetch: boolean;
  className?: string;
}

const Infinitescroll = ({
  children,
  next,
  hasMore,
  className,
  //fetch is a condition for fetching. this is useful when multiple components are conditionally rendered in a page
  fetch,
}: Props) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [isBottomCloseToViewport, setIsBottomCloseToViewport] = useState(false);

  useEffect(() => {
    //a block of code for fetching paginated data if the extra pages are still available and the bottom is less than 800pixels out of the viewport
    const handleScroll = () => {
      if (containerRef.current) {
        const { bottom } = containerRef.current.getBoundingClientRect();
        setIsBottomCloseToViewport(bottom < 400);
      }
    };
    window.addEventListener("scroll", handleScroll);
    if (hasMore && fetch && isBottomCloseToViewport) {
      next();
    }
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isBottomCloseToViewport]);

  return (
    <div ref={containerRef} className={`scroll ${className}`}>
      {children}
    </div>
  );
};

export default Infinitescroll;

import { Paginated } from "src/api/config/api.d";
import { convertUTCTimeToLocalTimeV2 } from "@/utils/dateUtils";
import { getPastExams } from "@/api/repository/exam-management/get-past-exams";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { ExamCard } from "@/api/repository/exam-management/exam";

export const useGetPastExams = (page: number, perPage: number) => {
  const query = useQuery({
    queryKey: ["getPastExams"],
    queryFn: () => getPastExams(page, perPage),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const {
        data,
        currentPage,
        nextPage,
        perPage,
        prevPage,
        total,
        totalPages,
      } = response.data as Paginated<ExamCard[]>;
      const examWithTag = data.map((exam) => ({
        ...exam,
        duration_window_end_date: convertUTCTimeToLocalTimeV2(
          exam.duration_window_end_date as string,
        ),
        duration_window_start_date: convertUTCTimeToLocalTimeV2(
          exam.duration_window_start_date as string,
        ),
        tags: ["CLOSED"] as ("INELIGIBLE" | "ELIGIBLE" | "LIVE" | "CLOSED")[],
      }));
      return {
        examWithTag,
        meta: {
          currentPage,
          nextPage,
          perPage,
          prevPage,
          total,
          totalPages,
        },
      };
    },
  });
  return query;
};

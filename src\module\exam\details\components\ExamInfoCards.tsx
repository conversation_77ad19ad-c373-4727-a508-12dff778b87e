import React from "react";
import ExamInfoCard from "./ExamInfoCard";
import { Grid } from "@chakra-ui/react";
import { ExamInfoCardProps } from "../ExamDetails";

const ExamInfoCards = ({ examInfo }: { examInfo: ExamInfoCardProps[] }) => {
  return (
    <Grid templateColumns="repeat(4, 1fr)" gap={4} px="100px" mb="16px">
      {examInfo.map((info, index) => (
        <ExamInfoCard
          key={index.toString()}
          title={info.title}
          subtitle={info.subtitle}
          value={info.value}
          icon={info.icon}
        />
      ))}
    </Grid>
  );
};

export default ExamInfoCards;

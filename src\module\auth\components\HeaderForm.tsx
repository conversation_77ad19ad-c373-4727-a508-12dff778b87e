import { FC } from "react";

import { IHeaderFormProps } from "./HeaderForm.d";
import { Flex, Image, Text } from "@chakra-ui/react";
import { BaseColor } from "@/constants/colors";
const HeaderForm: FC<IHeaderFormProps> = ({ title, subTitle }) => {
  return (
    <Flex
      flexDir="column"
      mt={{ base: "24px" }}
      alignItems="center"
      justifyContent="center"
      gap="8px"
    >
      <Image
        width={{ md: "136px", base: "135px" }}
        src="./images/miva-login-logo.png"
        alt="logo-miva"
      />
      <Text mt="40px" fontWeight="bold" color="white" fontSize="24px">
        {title}
      </Text>
      {subTitle && (
        <Text textAlign="center" color={BaseColor.PRIMARY_100} fontSize="16px">
          {subTitle}
        </Text>
      )}
    </Flex>
  );
};

export default HeaderForm;

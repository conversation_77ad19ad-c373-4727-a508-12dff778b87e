import { FC } from "react";
import { Textarea, Text } from "@chakra-ui/react";

import { ITextareaFieldProps } from "./TextareaField.d";

const TextareaField: FC<ITextareaFieldProps> = ({
  label,
  onChange,
  isInvalid = false,
  placeholder,
  ...inputProps
}) => {
  return (
    <div>
      {!!label && (
        <Text mb="8px" fontWeight="600" color="#0A3150" fontSize="14px">
          {label}
        </Text>
      )}
      <Textarea
        errorBorderColor={isInvalid ? "crimson" : undefined}
        placeholder={placeholder ?? label}
        size="lg"
        onChange={onChange}
        {...inputProps}
      />
    </div>
  );
};

export default TextareaField;

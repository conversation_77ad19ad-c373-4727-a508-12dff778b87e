import { baseApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, ManagementRoutes } from "src/api/config/routes";
import { ExamCard } from "../exam-management/exam";

export const getExamDetails = async (id: string) => {
  try {
    const response = await baseApi.get<APIResponse<ExamCard>>(
      `${ManagementRoutes[MODULE_ROUTE.STUDENT].DETAILS}${id}`,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching students:", error);
    throw error;
  }
};

export const removeSpecialCharacters = (character: string) => {
  return character
    ?.replace(/[^a-zA-Z0-9 ]/g, "")
    ?.replace(/([a-z])([A-Z])/g, "$1 $2")
    ?.trim();
};

export const getInitials = (first_name: string, last_name: string): string => {
  if (!first_name && !last_name) return "";
  const firstInitial = first_name?.charAt(0).toUpperCase();
  const lastInitial = last_name?.charAt(0).toUpperCase();
  return [firstInitial, lastInitial].filter(Boolean).join(" ");
};

export const capitalize = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
};

export const formatAmount = (str: string | number): string => {
  return Number(str).toLocaleString("en-US", {
    minimumFractionDigits: 2,
  });
};

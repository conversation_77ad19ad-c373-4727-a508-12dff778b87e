import { FC } from "react";
import { Input, InputGroup, InputLeftAddon, Text } from "@chakra-ui/react";

import { IInputFieldProps } from "./InputField.d";

const InputField: FC<IInputFieldProps> = ({
  label,
  onChange,
  isInvalid = false,
  suffix,
  prefix,
  placeholder,
  fontSize = "14px",
  required,
  containerClassName,
  ...inputProps
}) => {
  return (
    <div className={containerClassName}>
      {!!label && (
        <Text
          mb="8px"
          fontWeight="600"
          color="#0A3150"
          fontSize={fontSize || "14px"}
        >
          {label}{" "}
          {required && (
            <Text as="span" color="red.500">
              *
            </Text>
          )}
        </Text>
      )}
      <InputGroup size={inputProps.size || "lg"}>
        {prefix && (
          <InputLeftAddon bg={inputProps.backgroundColor || "transparent"}>
            {prefix}
          </InputLeftAddon>
        )}
        <Input
          errorBorderColor={isInvalid ? "crimson" : undefined}
          placeholder={placeholder ?? label}
          size="lg"
          onChange={onChange}
          fontSize={fontSize}
          {...inputProps}
        />
      </InputGroup>
      {!!suffix && (
        <span className="absolute right-3 top-2/3 -translate-y-1/2 text-sm text-gray-500">
          {suffix}
        </span>
      )}
    </div>
  );
};

export default InputField;

import { AxiosError } from "axios";

export function getFormattedTime(date: Date) {
  if (date === null) return "-";

  const dateValue = new Date(date);

  let hours: number = dateValue.getHours();
  const minutes: number = dateValue.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12;
  hours = hours ? hours : 12;
  const formatter_minutes = `0${minutes}`.slice(-2);

  return `${hours}:${formatter_minutes} ${ampm}`;
}

export const capitalizeFirstLetter = (s: string): string => {
  return s?.charAt(0).toUpperCase() + s?.slice(1);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const extractAxiosError = (error: AxiosError<any, any>): string => {
  if (typeof error.response?.data.errors == "string") {
    return capitalizeFirstLetter(error.response?.data.errors).replaceAll(
      "_",
      " ",
    );
  }
  if (typeof error.response?.data.errors == "object") {
    return capitalizeFirstLetter(
      Object.values(error.response?.data.errors).join(", "),
    ).replaceAll("_", " ");
  }
  return capitalizeFirstLetter(error.message).replaceAll("_", " ");
};

export const getBy = <T extends object, K extends keyof T>(
  data: T[],
  key: K,
  value?: string,
): T | undefined => {
  return data.find((item) => item[key] === value);
};

export const jsonToCSV = (jsonData: Record<string, any>[]): string => {
  if (!jsonData.length) return "";

  const keys = Object.keys(jsonData[0]); // Extract headers from the first object
  const header = keys.join(","); // Create the header row
  const csvRows = jsonData.map(
    (row) =>
      keys
        .map(
          (key) =>
            JSON.stringify(row[key], (_, value) =>
              value === null ? "" : value,
            ), // Handle null values
        )
        .join(","), // Join the row values with commas
  );

  return [header, ...csvRows].join("\n"); // Combine header and rows
};

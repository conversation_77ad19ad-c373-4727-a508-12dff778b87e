"use client";
import { FormikBag, withFormik } from "formik";
import LoginVerifyForm from "./LoginVerifyForm";

import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { IValidations } from "@/lib/utils/validation.d";
import { ILoginVerifyContainerProps, ILoginVerifyValue } from "./Login";

const validateFields: IValidations<ILoginVerifyValue> = {
  code: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
};

export const onSubmit = async (
  values: ILoginVerifyValue,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<ILoginVerifyContainerProps, ILoginVerifyValue>,
) => {
  setSubmitting(true);
  try {
    await props.handleLoginVerify(values);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    console.warn(e);
    setErrors(e);
  }
};

const LoginVerifyFormContainer = withFormik<
  ILoginVerifyContainerProps,
  ILoginVerifyValue
>({
  mapPropsToValues: () => {
    return { code: "" };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
})(LoginVerifyForm);

export default LoginVerifyFormContainer;

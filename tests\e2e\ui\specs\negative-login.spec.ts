import { test } from "@playwright/test";
import LoginPage from "../pages/login-page";
import uiPages from "../../utils/ui-pages";

// const email = process.env.EMAIL!;
// const password = process.env.PASSWORD!;

const email = "<EMAIL>";
const password = "Qa1@miva ";
let loginPage: LoginPage;

test.use({ storageState: { cookies: [], origins: [] } });

test.beforeEach(async ({ page }) => {
  loginPage = new LoginPage(page);
  await page.goto(uiPages.login);
});

test.describe("Invalid login", () => {
  test("login - invalid email", { tag: ["@negative"] }, async () => {
    await loginPage.doInvalidEmailLogin(password);
    await loginPage.assertInvalidCredentialMessage();
  });

  test("login - invalid password", { tag: ["@negative"] }, async () => {
    await loginPage.doInvalidPasswordLogin(email);
    await loginPage.assertInvalidCredentialMessage();
  });
});

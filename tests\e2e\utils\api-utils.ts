import playwright from "playwright";
import apiMethods from "./api-methods";
import { executeRequest } from "./api-requests-utils";
import { getVerificationCode } from "./email-utils";

async function initializeRequestContext() {
  return await playwright.request.newContext({
    baseURL: "https://sis-be.staging.miva.university",
  });
}

async function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Login Request
export async function loginRequest(email: string, password: string) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      email,
      password: password,
    },
  };

  try {
    const response = await executeRequest(
      apiContext,
      "/auth/login",
      apiMethods.post,
      requestOptions,
    );

    if (!response.ok()) {
      console.error(`Failed to submit login request: ${response.status()}`);
      return null;
    }
    const body = await response.json();
    return body;
  } catch (error) {
    console.error("Failed to submit login request:", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

// Verify login
export async function verifyLogin(email: string, verificationCode: string) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      email,
      code: verificationCode,
    },
  };

  try {
    const response = await executeRequest(
      apiContext,
      "/auth/login/verify",
      apiMethods.post,
      requestOptions,
    );

    if (!response.ok()) {
      console.error(`Failed to verify OTP and login: ${response.status()}`);
      return null;
    }
    const body = await response.json();
    return body;
  } catch (error) {
    console.error("Failed to verify OTP and login:", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

/**
 * @param {string} email The email address of the account to reset password
 * @returns {APIResponse} response
 * @throws {Error}
 */
export async function resetPasswordRequest(email: string) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      email,
    },
  };

  try {
    const response = await executeRequest(
      apiContext,
      "/auth/request-password-reset",
      apiMethods.post,
      requestOptions,
    );

    if (!response.ok()) {
      console.error(
        `Failed to send password reset request: ${response.status()}`,
      );
      return null;
    }
    const body = await response.json();
    return body;
  } catch (error) {
    console.error("Failing to send password reset request", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

/**
 * @param {string} email The email address of the account to reset password
 * @returns {APIResponse} response
 * @throws {Error}
 */
export async function verifyOtpAndSetNewPassword(
  email: string,
  password: string,
  confirmPassword: string,
  verificationCode: string,
) {
  const apiContext = await initializeRequestContext();
  const requestOptions = {
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      email,
      password,
      confirm_password: confirmPassword,
      reset_token: verificationCode,
    },
  };

  try {
    const response = await executeRequest(
      apiContext,
      "/auth/reset-password",
      apiMethods.post,
      requestOptions,
    );

    if (!response.ok()) {
      console.error(`Failed to set new password: ${response.status()}`);
      return null;
    }
    const body = await response.json();
    return body;
  } catch (error) {
    console.error("Failing to set new password", error);
    return null;
  } finally {
    await apiContext.dispose();
  }
}

// Exposed Utilities

export async function apiLogin(email: string, password: string) {
  try {
    await loginRequest(email, password);
    await delay(2000);
    const verificationCode = await getVerificationCode(email, "loginRequest");
    return verifyLogin(email, verificationCode);
  } catch (error) {
    console.error("Failed to login:", error);
    return null;
  }
}

export async function resetPassword(
  email: string,
  password: string,
  confirmPassword: string,
) {
  try {
    await resetPasswordRequest(email);
    await delay(2000);
    const verificationCode = await getVerificationCode(
      email,
      "resetPasswordRequest",
    );
    console.log(verificationCode);
    return await verifyOtpAndSetNewPassword(
      email,
      password,
      confirmPassword,
      verificationCode,
    );
  } catch (error) {
    console.error("Failed to reset password:", error);
    return null;
  }
}

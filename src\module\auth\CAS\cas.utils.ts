import { APIResponse, Paginated } from "src/api/config/api.d";
import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { AuthUser } from "src/store/AuthenticationStore/authentication.d";
import { CASService } from "./cas.d";
import { undefinedToString } from "@/lib/utils/string";

export const getUserByAccessToken = async (
  accessToken: string,
): Promise<AuthUser> => {
  const user = (
    await baseApi.get<APIResponse<AuthUser>>(
      Routes[MODULE_ROUTE.AUTH].AUTH_ME,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    )
  ).data.data;

  return user;
};

export const validateCasService = async (
  service: string,
): Promise<CASService> => {
  const services = (
    await baseApi.get<APIResponse<Paginated<CASService[]>>>(
      Routes[MODULE_ROUTE.CAS].LIST,
      {
        params: { search: service },
      },
    )
  ).data.data;
  return services.data[0];
};

export const getXMLFailureResponse = (code: string, message: string) => {
  return `<cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">
    <cas:authenticationFailure code="${code}">
      ${message}
    </cas:authenticationFailure>
  </cas:serviceResponse>
  `;
};

export const getJSONFailureResponse = (code: string, message: string) => {
  return {
    serviceResponse: {
      authenticationFailure: {
        code,
        description: message,
      },
    },
  };
};

export const getXMLSuccessResponse = (
  email: string,
  proxyGrantingTicket?: string,
) => {
  return `<cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">
    <cas:authenticationSuccess>
      <cas:user>${email}</cas:user>
      <cas:proxyGrantingTicket>${undefinedToString(proxyGrantingTicket)}</cas:proxyGrantingTicket>
    </cas:authenticationSuccess>
  </cas:serviceResponse>
  `;
};

export const getJSONSuccessResponse = (
  email: string,
  proxyGrantingTicket?: string,
) => {
  return {
    serviceResponse: {
      authenticationSuccess: {
        user: email,
        proxyGrantingTicket,
      },
    },
  };
};

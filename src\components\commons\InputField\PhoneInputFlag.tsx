import {
  Button,
  Flex,
  Image,
  Input,
  InputGroup,
  InputLeftAddon,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Stack,
  Text,
} from "@chakra-ui/react";
import { ChevronDown } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { splitCountryCode } from "../../../utils/commons";
import { Country } from "country-state-city";
import { useVirtualizer } from "@tanstack/react-virtual";

// Types
interface CountryItem {
  name: string;
  short: string;
  prefix: string;
}

interface PhoneInputFlagProps {
  label: string;
  onChange: (phone: string, dialCode: string) => void;
  error?: string;
  value?: string;
  disabled?: boolean;
}

const globalCountries = Country.getAllCountries();
const sortedCountries: CountryItem[] = globalCountries
  .map((item) => ({
    name: item.name,
    short: item.isoCode.toLowerCase(),
    prefix: item.phonecode.startsWith("+")
      ? item.phonecode
      : `+${item.phonecode}`,
  }))
  .sort((a, b) => {
    const prefixA = parseInt(a.prefix.replace("+", ""), 10);
    const prefixB = parseInt(b.prefix.replace("+", ""), 10);
    return prefixA - prefixB;
  });

const debounce = <T extends (...args: any[]) => void>(fn: T, ms = 300) => {
  let timeoutId: ReturnType<typeof setTimeout>;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(this, args), ms);
  };
};

export const PhoneInputFlag: React.FC<PhoneInputFlagProps> = ({
  label,
  onChange,
  error = undefined,
  value,
  disabled = false,
}) => {
  const [country, setCountry] = useState<CountryItem>(sortedCountries[98]);
  const [phone, setPhone] = useState<string>("");
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const scrollRef = useRef<HTMLDivElement>(null);

  const filteredCountries = useMemo(() => {
    if (!searchQuery) return sortedCountries;
    const query = searchQuery.toLowerCase();
    return sortedCountries.filter(
      (item) =>
        item.name.toLowerCase().includes(query) || item.prefix.includes(query),
    );
  }, [searchQuery]);

  // Setup virtualizer
  const rowVirtualizer = useVirtualizer({
    count: filteredCountries.length,
    getScrollElement: () => scrollRef.current,
    estimateSize: () => 35,
    overscan: 5,
  });

  const handlePhoneChange = useMemo(
    () =>
      debounce((valueInput: string, countryPrefix: string) => {
        onChange(valueInput, countryPrefix);
      }, 300),
    [onChange],
  );

  const handleCountrySelect = useCallback(
    (countryItem: CountryItem) => {
      setCountry(countryItem);
      setPhone("");
      handlePhoneChange("", countryItem.prefix.trim());
      setIsMenuOpen(false);
      setSearchQuery("");
    },
    [handlePhoneChange],
  );

  useEffect(() => {
    if (value) {
      const { countryCode, phoneNumber } = splitCountryCode(
        value,
        country.prefix,
      );
      const countrySupport = sortedCountries.find(
        (item) => item.prefix === countryCode,
      );
      setPhone(phoneNumber);
      setCountry(
        countrySupport || {
          name: "",
          short: "",
          prefix: countryCode,
        },
      );
    } else {
      if (phone) {
        setPhone("");
        setCountry(sortedCountries[98]);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, country.prefix]);

  return (
    <Stack width="100%" display="block">
      <Text color="#301446" fontSize="18px" fontWeight={600}>
        {label}
      </Text>
      <InputGroup w={"100%"} size="lg" zIndex="998">
        <InputLeftAddon background="#fff" pr="8px" borderRight="1px solid #ddd">
          <Menu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)}>
            <MenuButton
              as={Button}
              p={0}
              background="transparent"
              _hover={{ background: "transparent" }}
              _active={{ background: "transparent" }}
              onClick={() => setIsMenuOpen(true)}
              isDisabled={disabled}
            >
              <Flex flexDir="row" gap="6px" justifyContent="center">
                <Image
                  mt="4px"
                  width="20px"
                  height="15px"
                  src={`https://flagcdn.com/32x24/${country.short}.png`}
                  alt="flag"
                />
                <Text color="#301446" mt="3px" fontWeight={500} fontSize="14px">
                  {country.prefix}
                </Text>
                <span>
                  <ChevronDown className="w-3" />
                </span>
              </Flex>
            </MenuButton>
            <MenuList
              maxH="250px"
              overflowY="auto"
              zIndex="999"
              boxShadow="md"
              border="1px solid #ddd"
            >
              <Input
                placeholder="Search country or code..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                mx={2}
                my={2}
                width="90%"
                size="sm"
              />
              <div
                ref={scrollRef}
                style={{
                  height: "200px",
                  overflow: "auto",
                }}
              >
                <div
                  style={{
                    height: `${rowVirtualizer.getTotalSize()}px`,
                    width: "100%",
                    position: "relative",
                  }}
                >
                  {rowVirtualizer.getVirtualItems().map((virtualRow) => {
                    const countryItem = filteredCountries[virtualRow.index];
                    return (
                      <MenuItem
                        key={virtualRow.key}
                        _disabled={{ background: "rgba(239, 239, 239, 0.3)" }}
                        disabled={disabled}
                        onClick={() => handleCountrySelect(countryItem)}
                        position="absolute"
                        top={0}
                        left={0}
                        width="100%"
                        height={`${virtualRow.size}px`}
                        transform={`translateY(${virtualRow.start}px)`}
                      >
                        <Flex flexDir="row" gap="10px" justifyContent="center">
                          <Image
                            mt="7px"
                            width="20px"
                            height="15px"
                            src={`https://flagcdn.com/32x24/${countryItem.short}.png`}
                            alt="flag"
                          />
                          <Text color="#313848" mt="3px" fontSize="14px">
                            {countryItem.prefix} ({countryItem.name})
                          </Text>
                        </Flex>
                      </MenuItem>
                    );
                  })}
                </div>
              </div>
            </MenuList>
          </Menu>
        </InputLeftAddon>
        <Input
          _disabled={{ background: "rgba(239, 239, 239, 0.3)" }}
          disabled={disabled}
          type="tel"
          size="lg"
          fontSize={14}
          value={phone}
          placeholder=""
          onChange={(event) => {
            const valueInput = event.target.value;
            setPhone(valueInput);
            handlePhoneChange(valueInput, country.prefix);
          }}
        />
      </InputGroup>

      {error ? (
        <Text color="red" fontSize="14px">
          {error}
        </Text>
      ) : null}
    </Stack>
  );
};

export default PhoneInputFlag;

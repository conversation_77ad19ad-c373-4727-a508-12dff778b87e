/**
 * Generates a random password containing uppercase letters, lowercase letters,
 * numbers, and special characters. Ensures the password includes at least one
 * character of each type.
 *
 * @param {number} length - The desired length of the generated password.
 *                          Must be at least 4 characters long to include all types.
 * @returns {string} A randomly generated password string of the specified length.
 *
 * @throws {Error} If the length is less than 4, an error is thrown.
 */
export function generateRandomPassword(length: number) {
  if (length < 4) {
    throw new Error(
      "Password length must be at least 4 characters to include all character types.",
    );
  }

  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const symbols = "!@#$%^&*()_+[]{}|;:,.<>?";

  let password = [
    uppercase[Math.floor(Math.random() * uppercase.length)],
    lowercase[Math.floor(Math.random() * lowercase.length)],
    numbers[Math.floor(Math.random() * numbers.length)],
    symbols[Math.floor(Math.random() * symbols.length)],
  ];

  const allChars = uppercase + lowercase + numbers + symbols;
  for (let i = 4; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * allChars.length);
    password.push(allChars[randomIndex]);
  }

  password = password.sort(() => Math.random() - 0.5);

  return password.join("");
}

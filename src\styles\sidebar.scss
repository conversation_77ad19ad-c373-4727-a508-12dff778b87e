.sidebar-menu-wrapper {
  background: #0A3150;
  color: #E7EAEE;

  .menu-headline {
    font-size: 14px;
    margin-left: 12px;
    display: flex;
    align-items: center;
    font-weight: 400;
  }

  .menu-sub {
    display: block;
    overflow: hidden;
    max-height: 0;
    transition: max-height 300ms ease-in-out;

    .menu-item {
      padding-left: 25px;
      padding-top: 4px;
      padding-bottom: 4px;
    }
  }

  .menu-title-content {
    >.side-bar-icon-wrapper {
      >.menu-headline-group {
        >.menu-headline-dropdown {
          transform: rotateZ(0deg);
          transition: transform 300ms ease-in-out;
        }
      }
    }
  }

  .show {
    .menu-sub {
      &.menu-sub-accordion {
        max-height: 100vh;
        transition: max-height 300ms ease-in-out;
        display: flex;
        flex-direction: column;
      }
    }

    .menu-title-content {
      >.side-bar-icon-wrapper {
        >.menu-headline-group {
          >.menu-headline-dropdown {
            transform: rotateZ(90deg);
            transition: transform 300ms ease-in-out;
          }
        }
      }
    }
  }

  .menu-item {
    padding: 7px 0px;

    &.is-active {
      background: #072339;
    }
  }

  .menu-link {

    &.active {
      background: #072339;
    }
  }

  &.collapsed {
    width: 20px;

    .menu-content {
      display: none;
    }
  }
}
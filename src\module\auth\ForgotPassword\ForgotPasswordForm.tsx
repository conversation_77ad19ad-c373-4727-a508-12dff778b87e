import { Flex, Box, Stack, Text } from "@chakra-ui/react";
import { FormikProps } from "formik";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import { IForgotPasswordValue } from "./ForgotPassword.d";
import InputField from "@/components/commons/InputField/InputField";
import { BaseColor } from "@/constants/colors";
import { InputError } from "@/components/commons/InputField/InputError";

const ForgotPasswordForm = (props: FormikProps<IForgotPasswordValue>) => {
  const {
    touched,
    errors,
    handleSubmit,
    values,
    handleChange,
    handleBlur,
    isSubmitting,
  } = props;
  const { email: emailTouched } = touched;
  const { email: emailError } = errors;

  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Forgot Password?"
        subTitle="Don’t worry, we’ll send you reset instructions"
      />
      <Text color={"#FFCC80"} fontSize={11} paddingTop={3} textAlign={"center"}>
        This password change will also apply to your SIS account.
      </Text>
      <Box mt="48px">
        <InputField
          placeholder="Email Address"
          borderColor={BaseColor.PRIMARY_400}
          color="white"
          _placeholder={{ color: BaseColor.PRIMARY_400 }}
          name="email"
          onChange={handleChange}
          onBlur={handleBlur}
          value={values.email}
        />
        <InputError touched={emailTouched} error={emailError} />
      </Box>
      <Stack mt="48px">
        <ButtonCTA
          className="bg-[#BB9E7F] text-[12px] font-bold text-white"
          onClick={() => handleSubmit()}
          isLoading={isSubmitting}
        >
          Reset password
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ForgotPasswordForm;

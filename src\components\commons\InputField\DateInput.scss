.date-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  width: 50px;
  height: 50px;
  border-width: thin;
  position: absolute;
  left: -50px;
}

.react-datepicker {
  font-family: var(--font-primary);
}

.react-datepicker__header__dropdown--select {
  position: absolute;
  left: 0;
  right: 0;
  background-color: #fff;
  top: 3px;
  padding: 4px;
}

.react-datepicker__month-select,
.react-datepicker__year-select {
  padding: 2px;
  border-radius: 4px;
}

.react-datepicker__month-dropdown-container--select,
.react-datepicker__year-dropdown-container--select {
  margin: 0 10px;
}

.react-datepicker__triangle {
  display: none;
}

.react-datepicker__header {
  background-color: #fff;
  border-bottom: 1px solid #fff;
}

.react-datepicker__navigation-icon::before {
  border-color: #0a3150;
  border-width: 1px 1px 0 0;
  height: 8px;
  width: 8px;
  top: 11px;
}

.react-datepicker__day-names {
  margin-top: 12px;
}

.react-datepicker__day-names .react-datepicker__day-name {
  color: #64758b;
}

.react-datepicker__day--outside-month {
  color: #64758b;
}

.react-datepicker__day--disabled {
  color: #ccc;
}

.react-datepicker__day {
  font-size: 14px;
  border-radius: 6px;
}

.react-datepicker__day--selected {
  background-color: #0a3150;
}

.react-datepicker__day--today {
  background-color: #f1f5f9;
}

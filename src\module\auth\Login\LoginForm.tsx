import { FormikProps } from "formik";
import { Flex, Stack, Box, Text } from "@chakra-ui/react";

import HeaderForm from "../components/HeaderForm";
import InputField from "@/components/commons/InputField/InputField";
import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import { BaseColor } from "@/constants/colors";
import { InputError } from "@/components/commons/InputField/InputError";

import { EyeOpenIcon } from "@radix-ui/react-icons";
import { ILoginValue } from "./Login.d";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useCASQueryParams } from "../CAS/useCASQueryParams";

const LoginForm = (props: FormikProps<ILoginValue>) => {
  const router = useRouter();
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const { queryString } = useCASQueryParams();
  const handleForgetPassword = () => {
    router.push(`forgot-password${queryString}`);
  };
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const { email: emailTouched, password: passwordTouched } = touched;
  const { email: emailError, password: passwordError } = errors;
  const togglePasswordView = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };
  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm title="Log In" subTitle="Enter your account details" />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Email Address"
            borderColor={BaseColor.PRIMARY_400}
            color="white"
            _placeholder={{ color: BaseColor.PRIMARY_400 }}
            name="email"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.email}
          />
          <InputError error={emailError} touched={emailTouched} />
        </Box>
        <Box>
          <div className="relative">
            <InputField
              isInvalid
              placeholder="Password"
              borderColor={BaseColor.PRIMARY_400}
              color="white"
              _placeholder={{ color: BaseColor.PRIMARY_400 }}
              type={isPasswordVisible ? "text" : "password"}
              name="password"
              onChange={handleChange}
              onBlur={handleBlur}
              value={values.password}
            />
            <span className="absolute right-3 top-4 z-[9999] !cursor-pointer">
              <EyeOpenIcon
                className="h-4 w-4 text-white"
                onClick={() => togglePasswordView()}
              />
            </span>
          </div>
          <InputError error={passwordError} touched={passwordTouched} />
        </Box>
      </Stack>
      <Text
        cursor="pointer"
        onClick={handleForgetPassword}
        mt="12px"
        textAlign="right"
        fontSize="14px"
        color={"white"}
      >
        Forgot Password?
      </Text>
      <Stack mt="48px">
        <ButtonCTA
          type="submit"
          isLoading={isSubmitting}
          onClick={() => {
            handleSubmit();
          }}
          variant={EButtonType.DEFAULT}
        >
          Login
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default LoginForm;

import { Box, Container, Flex, Image, Text } from "@chakra-ui/react";

const RightImageBlock = () => {
  return (
    <Box
      mr="20px"
      borderRadius="24px"
      objectFit="cover"
      display={{ lg: "flex", md: "none", base: "none" }}
      width={{ xl: "688px", md: "524px" }}
      height={{ xl: "768px", md: "620px" }}
      justifyItems="flex-end"
      alignItems="flex-end"
      backgroundImage="url(./images/login-image-banner.png)"
    >
      <Container m={{ lg: "48px", md: "16px" }} p="unset">
        <Flex
          flexDir="column"
          justifyItems="flex-start"
          alignItems="flex-start"
          mb="24px"
        >
          <Text
            fontWeight="bold"
            fontSize={{ xl: "42px", base: "30px" }}
            color="white"
            className="font-secondary uppercase"
          >
            Student Exam Portal
          </Text>
          <Text
            fontWeight="Medium"
            fontSize={{ xl: "20px", base: "18px" }}
            color="rgb(255 255 255 / 80%)"
          >
            Affordable higher education you can take wherever life takes you.
            Learn anywhere at your own pace.
          </Text>
        </Flex>
        <Box
          display="flex"
          alignItems="center"
          px={{ lg: "32px", md: "16px" }}
          py="16px"
          gap={{ lg: "16px", md: "16px" }}
          border="solid 1px rgb(255 255 255 / 50%)"
          backgroundColor="rgb(255 255 255 / 14%)"
          borderRadius="24px"
          backdropFilter="auto"
          backdropBlur="21px"
        >
          <Image
            width={{ lg: "72px", md: "48px" }}
            height={{ lg: "72px", md: "48px" }}
            src="./images/licensed.png"
            alt="licensed"
          />
          <Text fontSize="18px" color="rgb(255 255 255 / 80%)">
            Miva Open University is licensed by the National Universities
            Commission
          </Text>
        </Box>
      </Container>
    </Box>
  );
};

export default RightImageBlock;

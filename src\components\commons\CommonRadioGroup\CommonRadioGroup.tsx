"use client";

import { FC } from "react";

import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ICommonRadioGroupProps } from "./CommonRadioGroup.d";

const CommonRadioGroup: FC<ICommonRadioGroupProps> = ({
  data,
  defaultValue,
  onChange,
}) => {
  return (
    <RadioGroup defaultValue={defaultValue || data[0].value}>
      {data.map((item, index) => {
        return (
          <div
            key={`radio-item-${item.value}`}
            className="flex items-center space-x-2"
          >
            <RadioGroupItem
              onChange={(e) => {
                console.log("RadioGroup -->", e);
                onChange(item.value);
              }}
              value={item.value}
              id={`radio-${index}-${item.value}`}
            />
            <Label htmlFor={`radio-${index}-${item.value}`}>{item.label}</Label>
          </div>
        );
      })}
    </RadioGroup>
  );
};

export default CommonRadioGroup;

/**
 * Module routes to point to correct URL of specific service
 */
enum MODULE_ROUTE {
  APPLICATION = "application",
  AUDIT = "audit",
  COURSE = "course",
  COURSE_OFFERING = "courseOffering",
  COHORT = "cohort",
  AUTH = "auth",
  ENROLMENT = "enrollment",
  DEFERMENT = "deferment",
  STUDENT = "student",
  PEOPLE = "user",
  ROLES = "roles",
  STAFF = "staff",
  PROGRAMME = "programme",
  ACADEMIC_TIME_PERIOD = "academic-time-period",
  FACULTY = "faculty",
  DEPARTMENT = "department",
  SEMESTER = "semester",
  LEVEL = "level",
  BUNDLE = "bundle",
  GRADE_SCALE = "gradeScale",
  RESULT_TEMPLATE = "resultTemplate",
  PROGRAMME_LEVEL = "programmeLevel",
  PROGRAMME_INTAKE = "programmeIntake",
  MISCELLANE0US = "misc",
  REPORT = "report",
  BUNDLES = "bundles",
  VOUCHER = "voucher",
  FINANCE = "finance",
  RESULT = "result",
  EXAM_LOCATION = "exam-location",
  EXAM_PORTAL = "exam-portal",
  PERMISSIONS = "permissions",
  CAS = "cas-service",
}
///student/exams/past

const Routes = {
  [MODULE_ROUTE.AUTH]: {
    LOGIN: `sis/${MODULE_ROUTE.AUTH}/login`,
    AUTH_ME: `sis/${MODULE_ROUTE.AUTH}/me`,
    LOGIN_VERIFY: `sis/${MODULE_ROUTE.AUTH}/login/verify`,
    REQUEST_RESET: `sis/${MODULE_ROUTE.AUTH}/request-password-reset`,
    RESET: `sis/${MODULE_ROUTE.AUTH}/reset-password`,
    RESEND: `sis/${MODULE_ROUTE.AUTH}/request-verification`,
  },
  [MODULE_ROUTE.CAS]: {
    LIST: `${MODULE_ROUTE.CAS}/list`,
  },
  [MODULE_ROUTE.STUDENT]: {
    QUESTIONS: `${MODULE_ROUTE.STUDENT}/exams`,
  },
};

const ManagementRoutes = {
  [MODULE_ROUTE.STUDENT]: {
    UPCOMING: `${MODULE_ROUTE.STUDENT}/exams/upcoming`,
    PAST: `${MODULE_ROUTE.STUDENT}/exams/past`,
    DETAILS: `${MODULE_ROUTE.STUDENT}/exams/`,
  },
};

/** Example usage of Routes */
/*

async function createApplication(data: any) {
  try {
    const response = await baseApi.post(Routes.APPLICATION.CREATE, data);
    return response.data;
  } catch (error) {
    console.error("Error creating application:", error);
    throw error;
  }
}

async function updateApplication(id: string, data: any) {
  try {
    const response = await baseApi.put(Routes.APPLICATION.UPDATE(id), data);
    return response.data;
  } catch (error) {
    console.error("Error updating application:", error);
    throw error;
  }
}
*/

export { MODULE_ROUTE, Routes, ManagementRoutes };

/* eslint-disable @typescript-eslint/no-explicit-any */
import { ChangeEvent } from "react";

export const createSearchFunction = (data: any[]) => {
  return (event: ChangeEvent<HTMLInputElement>) => {
    const searchTerm = event.target.value.toLowerCase();
    return data.filter(
      (item) =>
        item.name.toLowerCase().includes(searchTerm) ||
        item.email.toLowerCase().includes(searchTerm) ||
        item.id.toLowerCase().includes(searchTerm),
    );
  };
};

export const createHandleSelectFunction = (data: any[]) => {
  return (value: string) => {
    if (value === "All") {
      return data;
    }
    return data.filter((item) => item.role === value);
  };
};

export const roleOptions = [
  { label: "All", value: "All" },
  { label: "Super Admin", value: "super_admin" },
  { label: "Admin", value: "admin" },
  { label: "Sale Officers", value: "sale_officers" },
  { label: "Admission Officer", value: "admissions_officer" },
  { label: "Faculty", value: "faculty" },
  { label: "Success Adviser", value: "success_adviser" },
  { label: "Student", value: "student" },
];

export const facultyOptions = [
  { label: "All", value: "All" },
  { label: "School of Computing", value: "school_of_computing" },
  {
    label: "School of Management and Social Sciences",
    value: "school_of_social_sciences",
  },
  {
    label: "School of Allied Health Sciences",
    value: "school_of_allied_health",
  },
];

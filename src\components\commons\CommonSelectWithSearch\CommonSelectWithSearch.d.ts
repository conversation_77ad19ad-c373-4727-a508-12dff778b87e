export interface IOption {
  label: string;
  value: string | number | object;
}

export interface ICommonSelectWithSearchProps {
  label?: React.ReactNode;
  labelContent?: string;
  value?: string | number | object | undefined;
  options: IOption[];
  onChange: (value: string) => void;
  handleSearch?: (value) => void;
  showIndicatorIcon?: boolean;
  className?: string;
  autoSelectFirstOption?: boolean;
  placeholder?: string;
  isDisabled?: boolean;
  defaultOption?: string;
}

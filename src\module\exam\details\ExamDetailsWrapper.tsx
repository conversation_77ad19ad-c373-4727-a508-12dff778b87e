"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";
import ExamTitle from "./components/ExamTitle";
import ExamInfoCards from "./components/ExamInfoCards";
import ExamInstructions from "./components/ExamInstructions";
import { convertUTCTimeToLocalTimeV2 } from "@/utils/dateUtils";
import IneligibilityInstructions from "./components/IneligibilityInstructions";
import { useStartExam } from "../session/useExam";
import { ReloadIcon } from "@radix-ui/react-icons";
import { Tooltip, useToast } from "@chakra-ui/react";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";
// import { Encryption } from "@/utils/encryption";

import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { useLocalStorage } from "src/store/LocalStorage/LocalStorage";
import { extractErrorFromApi } from "@/utils/commons";
import { useExamDetails } from "@/hooks/useExamDetails/useExamDetails";
import { browserUtils } from "@/utils/browserUtils";
import { ExamCard } from "../management/ExamManagement";

const isTestingMode = true; // Only enable it when test at local - allow online exam to be turn on

const ExamDetails = () => {
  const toast = useToast();
  const params = useParams();
  const searchParams = useSearchParams();
  const examId = searchParams.get("exam_id") || params?.id || "";

  const router = useRouter();
  const startExamMutation = useStartExam();
  const [examAccessKey, setExamAccessKey] = useLocalStorage(
    "exam_access_key",
    null,
  );
  const { user } = useAuthStore();

  useEffect(() => {
    if (!examAccessKey) {
      const data = new Date().getTime();
      setExamAccessKey(data);
    }
  }, [examAccessKey]);

  const { isFetching, data } = useExamDetails(examId as string);

  const onClickStartExam = async () => {
    startExamMutation.mutate(
      { exam_id: examId as string },
      {
        onSuccess: (response) => {
          if (response?.data?.status === "OK") {
            toast({
              description: "Starting exam...",
              status: "success",
              duration: 1000,
            });
            // const encryptedExamId = Encryption.encryptDataWithAccessKey({
            //   keyId: String(params.id),
            //   randomAccessKey: examAccessKey,
            // });
            // const encryptedSessionId = Encryption.encryptDataWithAccessKey({
            //   keyId: String(response.data?.data),
            //   randomAccessKey: examAccessKey,
            // });
            // console.log("Exam ID", params.id);
            // console.log("Session ID", response.data?.data);

            const sessionId = response.data?.data;
            router.push(
              `/exam/session?session_id=${sessionId}&exam_id=${examId}&course_id=${data?.course_id}&page=1&limit=10`,
            );
          } else {
            toast({
              description: response?.data?.message,
              status: "error",
            });
          }
        },
        onError: (error) => {
          toast({
            //@ts-expect-error
            description: extractErrorFromApi(error?.response?.data?.errors),
            status: "error",
          });
        },
      },
    );
  };

  const onClickDeferExam = () => {
    window.open(`${process.env.NEXT_PUBLIC_SIS_URL}/deferments`);
  };

  const onClickDownloadExamBrowser = () => {
    window.open("https://safeexambrowser.org/download_en.html");
  };
  const onClickDownloadExamSlip = () => {
    console.log(process.env.NEXT_PUBLIC_QR_CODE_LINK);
    window.open(
      process.env.NEXT_PUBLIC_QR_CODE_LINK
        ? process.env.NEXT_PUBLIC_QR_CODE_LINK
        : "https://student-transcripts.staging.miva.university/show-qr",
    );
  };

  const examInfo = [
    {
      title: "Examination",
      subtitle: "Date",
      value:
        data && data.duration_window_start_date
          ? convertUTCTimeToLocalTimeV2(
              data.duration_window_start_date as string,
            )
          : "N/A",
      icon: "/images/icons/calendar2.svg",
    },
    {
      title: "Examination",
      subtitle: "Time",
      value:
        data && data.duration_window_start_date
          ? convertUTCTimeToLocalTimeV2(
              data.duration_window_start_date as string,
              true,
            )
          : "N/A",
      icon: "/images/icons/exam-details-clock.svg",
    },
    {
      title: "Number of",
      subtitle: "Questions",
      value: data ? data.no_questions?.toString() : "0",
      icon: "/images/icons/chat-info.svg",
    },
    {
      title: "Duration ",
      subtitle: "(Minutes)",
      value: data ? data.duration?.toString() : "0",
      icon: "/images/icons/hourglass.svg",
    },
  ];

  const renderExamMode = (data: ExamCard) => {
    if (data.taken) {
      return (
        <div className="flex w-[360px] items-center justify-center rounded-[3px] bg-[rgb(255,116,116)] px-4 py-4 text-white hover:bg-[#ff4f4f] hover:opacity-85">
          <p className="text-lg font-[16px]">
            This exam has already been completed{" "}
          </p>
        </div>
      );
    }
    if (isTestingMode) {
      return (
        <Tooltip
          label={
            data?.time_has_expired ? "Exam time has expired" : "Start Exam"
          }
        >
          <div className="">
            <Button
              className="flex w-[360px] items-center gap-x-2 bg-[#009966] px-10 py-6 uppercase hover:bg-[#009966] hover:opacity-85"
              onClick={onClickStartExam}
              disabled={startExamMutation.isPending || data?.time_has_expired}
            >
              {data.in_progress ? "Resume Exam" : "Start Exam"}
              {startExamMutation.isPending && (
                <ReloadIcon className="animate-spin" />
              )}
            </Button>
          </div>
        </Tooltip>
      );
    } else {
      return (
        <>
          <Button
            className="mr-6 w-[360px] border border-[#BDC2C9] py-6 text-sm uppercase"
            onClick={onClickDeferExam}
            variant="ghost"
          >
            Defer Exam
          </Button>
          <Button
            className="w-[360px] px-10 py-6 text-sm uppercase"
            onClick={onClickDownloadExamBrowser}
          >
            Download Exam Browser
          </Button>
        </>
      );
    }
  };

  const renderExamWithSebMode = (data: ExamCard) => {
    if (data.taken) {
      return (
        <div className="flex w-[360px] items-center justify-center rounded-[3px] bg-[rgb(255,116,116)] px-4 py-4 text-white hover:bg-[#ff4f4f] hover:opacity-85">
          <p className="text-lg font-[16px]">
            This exam has already been completed{" "}
          </p>
        </div>
      );
    }
    return (
      <Tooltip
        label={data?.time_has_expired ? "Exam time has expired" : "Start Exam"}
      >
        <div className="">
          <Button
            className="flex w-[360px] items-center gap-x-2 bg-[#009966] px-10 py-6 uppercase hover:bg-[#009966] hover:opacity-85"
            onClick={onClickStartExam}
            disabled={startExamMutation.isPending || data?.time_has_expired}
          >
            {data.in_progress ? "Resume Exam" : "Start Exam"}
            {startExamMutation.isPending && (
              <ReloadIcon className="animate-spin" />
            )}
          </Button>
        </div>
      </Tooltip>
    );
  };

  return (
    <div className="mx-auto flex h-full flex-col gap-6 pb-10">
      <ExamTitle title={data ? data.title : ""} />

      {isFetching ? (
        <CommonLoading />
      ) : (
        <>
          <ExamInfoCards examInfo={examInfo} />
          {!data?.is_eligible ? (
            <IneligibilityInstructions />
          ) : (
            <ExamInstructions
              sittingMode={data.sitting}
              isStudentAtNigeria={
                user?.contact_information.country === "Nigeria"
              }
            />
          )}

          <div className="flex flex-row justify-center">
            {data?.is_eligible ? (
              browserUtils.isSEBBrowser() ? (
                renderExamWithSebMode(data)
              ) : (
                <>
                  {data?.sitting === "ONLINE_ONLY" && renderExamMode(data)}
                  {data?.sitting === "ONLINE_AND_IN_PERSON" &&
                    user?.contact_information.country !== "Nigeria" && (
                      <>
                        <Button
                          className="mr-6 w-[360px] border border-[#BDC2C9] py-6 text-sm uppercase"
                          onClick={onClickDeferExam}
                          variant="ghost"
                        >
                          Defer Exam
                        </Button>
                        <Button
                          className="w-[360px] px-10 py-6 text-sm uppercase"
                          onClick={onClickDownloadExamBrowser}
                        >
                          Download Exam Browser
                        </Button>
                      </>
                    )}
                  {data?.sitting === "ONLINE_AND_IN_PERSON" &&
                    user?.contact_information.country === "Nigeria" && (
                      <>
                        <Button
                          className="mr-4 w-[360px] border border-[#BDC2C9] py-6 text-sm uppercase"
                          onClick={onClickDeferExam}
                          variant="ghost"
                        >
                          Defer Exam
                        </Button>
                        <Button
                          className="w-[360px] px-10 py-6 text-sm uppercase"
                          onClick={onClickDownloadExamSlip}
                        >
                          Download Exam Slip
                        </Button>
                      </>
                    )}
                </>
              )
            ) : (
              <div />
            )}
          </div>
        </>
      )}
    </div>
  );
};

const ExamDetailsWrapper = () => {
  return (
    <Suspense fallback={<CommonLoading />}>
      <ExamDetails />
    </Suspense>
  );
};

export default ExamDetailsWrapper;

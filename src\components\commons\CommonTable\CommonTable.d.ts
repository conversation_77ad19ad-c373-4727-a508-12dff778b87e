import { ColumnDef, OnChangeFn, VisibilityState } from "@tanstack/react-table";
import { ReactNode } from "react";

export type TPagination = {
  pageIndex: number;
  pageSize: number;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type TRowsData = { [key: string]: any };

export type TPagination = {
  pageIndex: number;
  pageSize: number;
};

export interface ICommonTableProps {
  isSimplePaging?: boolean;
  dataTable: TRowsData[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columnsTable: ColumnDef<T>[];
  handleSearch?: (valueSearch: string) => void;
  rowSelection?: TRowsData;
  totalNumOfPages?: number;
  // currentPage?: number;
  // pageSize?: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setRowSelection?: (value: any) => void;
  pagination?: TPagination;
  setPagination?: OnChangeFn<TPagination>;
  hidePagination?: boolean;
  manualPagination?: boolean;
  customFooter?: ReactNode;
  curveRowEdge?: boolean;
  containerClassName?: string;
  compact?: boolean;
  isLoading?: boolean;
  columnVisibility?: VisibilityState;
}

import { FC } from "react";
import { ICommonCardProps } from "./CommonCard.d";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";

const CommonCard: FC<ICommonCardProps> = ({
  title,
  customFooter,
  description,
  children,
  className,
}) => {
  return (
    <Card className={cn(className)}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
      )}
      <CardContent>{children}</CardContent>
      {!!customFooter && <CardFooter>{customFooter}</CardFooter>}
    </Card>
  );
};

export default CommonCard;

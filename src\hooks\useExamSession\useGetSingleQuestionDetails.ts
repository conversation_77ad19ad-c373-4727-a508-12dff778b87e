import { APIResponse } from "@/api/config/api.d";
import {
  GetSingleQuestionRequestType,
  GetSingleQuestionResponseType,
} from "@/api/repository/exam-session/get-single-question";
import { getSingleQuestionDetails } from "@/api/repository/exam-session/get-single-question-details";
import { useQuery } from "@tanstack/react-query";

export const QUERY_KEY_GET_SINGLE_QUESTION_DETAILS =
  "Get Single Question Details";

export const useGetSingleQuestionDetails = ({
  exam_id,
  session_id,
  question_id,
}: GetSingleQuestionRequestType) => {
  const query = useQuery({
    queryKey: [
      QUERY_KEY_GET_SINGLE_QUESTION_DETAILS,
      { exam_id, session_id, question_id },
    ],
    queryFn: () =>
      getSingleQuestionDetails({ exam_id, session_id, question_id }),
    enabled: !!exam_id && !!session_id && !!question_id,
    staleTime: 0,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
    select: (response: APIResponse<GetSingleQuestionResponseType>) =>
      response.data,
  });
  return query;
};

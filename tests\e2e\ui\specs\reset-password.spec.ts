import { test } from "@playwright/test";
import LoginPage from "../pages/login-page";
import uiPages from "../../utils/ui-pages";
import { generateRandomPassword } from "tests/e2e/utils/utils";
import ResetPasswordPage from "../pages/reset-password-page";

const email = "<EMAIL>";
const password = generateRandomPassword(12);
const confirmPassword = password;
let loginPage: LoginPage;
let resetPasswordPage: ResetPasswordPage;

test.use({ storageState: { cookies: [], origins: [] } });

test(
  "Ad<PERSON> can reset password",
  { tag: ["@smoke", "@positive"] },
  async ({ page }) => {
    loginPage = new LoginPage(page);
    resetPasswordPage = new ResetPasswordPage(page);

    await page.goto(uiPages.forgotPassword);
    await resetPasswordPage.resetPassword(email, password, confirmPassword);
    await resetPasswordPage.assertPasswordResetSuccess();
    // await loginPage.doLogin(email, password);
    // await loginPage.checkLoggedIn();
    await resetPasswordPage.assertUserCanLoginWithNewPassword(email, password);
  },
);

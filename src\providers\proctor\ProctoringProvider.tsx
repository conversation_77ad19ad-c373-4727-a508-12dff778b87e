"use client";

import React, { useEffect, useRef, useState } from "react";
import { Text } from "@chakra-ui/react";
import {
  Environment,
  Evidence,
  IProctoringManagerConfig,
  IStartSessionParams,
  PossibleViolation,
  ProctoringManager,
  TrackingSession,
  ViolationsTracker,
  MediaPermissions,
  IGetActivityMetricsParams,
  IActivityMetricResponse,
  ProctoringSocketManager,
  ProctoringSocketConfig,
} from "miva-proctor-sdk";
import { ProctoringContext, ViolationCallback } from "./ProctoringContext";
import { blobToBase64 } from "@/utils/fileUtils";
import { HStack, useToast, VStack } from "@chakra-ui/react";
import { useLocalStorage } from "src/store/LocalStorage/LocalStorage";
import { debounce } from "lodash";
import CommonDialog from "@/components/commons/CommonDialog/CommonDialog";
import { APIResponse } from "@/api/config/api.d";
import { browserUtils } from "@/utils/browserUtils";

const TIME_PING_SESSION = 5000;
const TIME_SEND_VIOLATIONS = 5000;
const TIME_DEBOUNCE_VIOLATIONS = 3000;
const socket_proctor_server_url = "wss://metrics.staging.miva.university"; // To be revert to ENV when the environment vars are updated
const socket_auth_token =
  process.env.NEXT_PUBLIC_AUTH_TOKEN_PROCTOR_SERVER_URL || "";

const ProctoringProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const proctorManagerRef = useRef<ProctoringManager | null>(null);
  const socketManagerRef = useRef<ProctoringSocketManager | null>(null);
  const trackingSessionRef = useRef<TrackingSession | null>(null);
  const violationsTrackerRef = useRef<ViolationsTracker | null>(null);
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState({
    audio: false,
    video: false,
    screen: false,
  });
  const [isProctoring, setIsProctoring] = useLocalStorage(
    "isProctoring",
    false,
  );
  const violationCallbackRef = useRef<ViolationCallback | null>(null);
  const [violationsData, setViolationsData] = useState<
    { violation: PossibleViolation; evidence: string }[]
  >([]);
  const intervalIdRef = useRef<{
    violationsInterval: NodeJS.Timeout | null;
    pingInterval: NodeJS.Timeout | null;
  }>({
    violationsInterval: null,
    pingInterval: null,
  });

  const toast = useToast();

  const getProctorManagerInstance = (config: IProctoringManagerConfig) => {
    if (!proctorManagerRef.current) {
      proctorManagerRef.current = new ProctoringManager(config);
    }
    return proctorManagerRef;
  };

  const onViolation = (callback: ViolationCallback) => {
    violationCallbackRef.current = callback;
  };

  const removeViolationListener = () => {
    violationCallbackRef.current = null;
  };

  useEffect(() => {
    intervalIdRef.current.violationsInterval = setInterval(() => {
      if (violationsData.length > 0) {
        proctorManagerRef.current?.onReceiveMultipleViolationTracker(
          violationsData,
        );
        setViolationsData([]);
      }
    }, TIME_SEND_VIOLATIONS);

    return () => {
      if (intervalIdRef.current.violationsInterval) {
        clearInterval(intervalIdRef.current.violationsInterval);
      }
    };
  }, [violationsData]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (isProctoring && socketManagerRef.current) {
      // intervalId = setInterval(() => {
      //   proctorManagerRef.current?.pingSession();
      // }, TIME_PING_SESSION);
      intervalId = intervalIdRef.current.pingInterval = setInterval(() => {
        socketManagerRef.current?.pingSession();
      }, TIME_PING_SESSION);
    }

    return () => {
      if (intervalIdRef.current.pingInterval) {
        clearInterval(intervalIdRef.current.pingInterval);
      }
    };
  }, [isProctoring]);

  const initProctor = (studentId: string, studentEmail: string) => {
    const configInitProctor: IProctoringManagerConfig = {
      userId: studentId,
      userEmail: studentEmail,
      env: Environment.Staging,
    };
    return getProctorManagerInstance(configInitProctor);
  };

  const initTrackingSession = () => {
    if (!trackingSessionRef.current) {
      const debouncedOnReceiveViolationTracker = debounce(
        (violation: PossibleViolation, evidence: Evidence) => {
          try {
            // Check if we can access Blob APIs
            if (
              evidence &&
              typeof evidence === "object" &&
              "size" in evidence
            ) {
              blobToBase64(evidence as Blob)
                .then((res) => {
                  setViolationsData((prevData) => [
                    ...prevData,
                    { violation, evidence: res as string },
                  ]);
                })
                .catch((error) => {
                  // Fallback: store violation without evidence
                  setViolationsData((prevData) => [
                    ...prevData,
                    { violation, evidence: "" },
                  ]);
                });
            } else {
              // Handle non-Blob evidence
              setViolationsData((prevData) => [
                ...prevData,
                { violation, evidence: "" },
              ]);
            }
          } catch (error) {
            // Fallback for any errors
            setViolationsData((prevData) => [
              ...prevData,
              { violation, evidence: "" },
            ]);
          }
        },
        TIME_DEBOUNCE_VIOLATIONS,
      );

      trackingSessionRef.current = {
        onPossibleViolation: function (
          violation: PossibleViolation,
          evidence?: Evidence,
        ): void {
          try {
            if (violation === PossibleViolation.AudioMuted) {
              console.log("Skip false alert for audio muted");
              return;
            }
            // Safe logging that won't be blocked by SEB
            console.log("Violation detected:", violation);
            const logMessage = `Violation detected: ${violation}`;
            if (typeof window !== "undefined" && window.console) {
              window.console.log(logMessage);
            }

            if (evidence) {
              // Only call callback if it exists
              if (violationCallbackRef.current) {
                violationCallbackRef.current(violation, evidence);
              }

              // Improve on safe evidence handling
              if (
                evidence instanceof Blob ||
                (typeof evidence === "object" && "size" in evidence)
              ) {
                debouncedOnReceiveViolationTracker(violation, evidence);
              } else {
                // Handle non-Blob evidence
                setViolationsData((prevData) => [
                  ...prevData,
                  { violation, evidence: "" },
                ]);
              }
            } else {
              // Handle case without evidence
              setViolationsData((prevData) => [
                ...prevData,
                { violation, evidence: "" },
              ]);
            }
          } catch (error) {
            // Fallback for any errors
            setViolationsData((prevData) => [
              ...prevData,
              { violation, evidence: "" },
            ]);
          }
        },
        onRandomPhoto: function (photo: Blob): void {
          try {
            if (typeof window !== "undefined" && window.console) {
              window.console.log("Random photo captured");
            }
          } catch (error) {
            // Silently handle errors in SEB
          }
        },
      };
    }
    return trackingSessionRef;
  };

  const initViolationsTracker = () => {
    if (!violationsTrackerRef.current) {
      violationsTrackerRef.current = new ViolationsTracker(
        initTrackingSession().current as TrackingSession,
      );
    }
    return violationsTrackerRef;
  };

  const initSocketManager = (sessionId: string, authToken: string) => {
    if (!socketManagerRef.current) {
      const socketConfig: ProctoringSocketConfig = {
        serverUrl: socket_proctor_server_url,
        authToken: authToken,
        sessionId: sessionId,
        session: {
          baseUrl: socket_proctor_server_url,
          id: sessionId,
          token: authToken,
          onPossibleViolation: (
            violation: PossibleViolation,
            evidence?: Evidence,
          ) => {
            console.log(
              "Managed via normal proctoringManagerRef already",
              violation,
            );
          },
          onRandomPhoto: (photo: Blob) => {
            console.log("Random photo taken via socket");
          },
        },
        reconnection: {
          enabled: true,
          maxAttempts: 5,
          delay: 1000,
        },
        events: {
          onSessionEnded: (data) => {
            console.log("Session ended via socket:", data);
            setIsProctoring(false);
          },
          onError: (error) => {
            console.error("Socket error:", error);
            toast({
              title: "Connection Error",
              description: "There was an error with the proctoring connection",
              status: "error",
              duration: 5000,
              isClosable: true,
            });
          },
          onPing: (data) => {
            console.log("Ping sent via socket:", data);
          },
          onPong: (data) => {
            console.log("Pong received via socket:", data);
          },
        },
      };

      socketManagerRef.current = new ProctoringSocketManager(socketConfig);
    }
    return socketManagerRef;
  };

  const checkPermissions = async (
    isSeb: boolean,
    hasCamera: boolean,
    proctorManager?: ProctoringManager,
  ) => {
    let permissionData: any = {};
    if (proctorManager) {
      permissionData = await proctorManager.getRequestPermissions();
      setPermissionStatus({
        audio: !!permissionData?.audio,
        video: !!permissionData?.camera,
        screen: !!permissionData.screen,
      });

      let allowToStart = false;
      if (isSeb) {
        allowToStart = hasCamera ? !!permissionData.camera : true;
      } else {
        allowToStart = hasCamera
          ? !!permissionData.audio &&
            !!permissionData.camera &&
            !!permissionData.screen
          : !!permissionData.audio && !!permissionData.screen;
      }
      return allowToStart;
    } else {
      permissionData = await MediaPermissions.getInstance();
      setPermissionStatus({
        audio: !!permissionData.getAudioPerm(),
        video: !!permissionData.getUserMediaStream(),
        screen: !!permissionData.getDisplayMediaStream(),
      });
      let allowToStart = false;
      if (isSeb) {
        // In SEB, audio and screen are optional
        // If hasCamera is true, require camera permission
        allowToStart = hasCamera ? !!permissionData.getUserMediaStream() : true;
      } else {
        // In non-SEB, all permissions are required
        allowToStart = hasCamera
          ? !!permissionData.getAudioPerm() &&
            !!permissionData.getUserMediaStream() &&
            !!permissionData.getDisplayMediaStream()
          : !!permissionData.getAudioPerm() &&
            !!permissionData.getDisplayMediaStream();
      }
      return allowToStart;
    }
  };

  const onStartProctoring = async (
    startSessionParams: IStartSessionParams,
  ): Promise<boolean> => {
    let startProctor = false;

    const config = {
      userId: startSessionParams.studentEmail,
      userEmail: startSessionParams.studentEmail,
      env: Environment.Staging,
    };

    console.log("On start proctoring...");
    if (!proctorManagerRef.current) {
      proctorManagerRef.current = new ProctoringManager(config);
    }

    const isSeb = browserUtils.isSEBBrowser();
    const checkCamera =
      await MediaPermissions.getInstance().checkCameraAvailability();

    if (checkCamera.isAvailable) {
      toast({
        description: "Camera hardware is available",
        status: "success",
      });
    } else {
      toast({
        description: checkCamera.message,
        status: "warning",
      });
    }

    // Check initial permissions
    const hasAllPermissions = await checkPermissions(
      isSeb,
      checkCamera.isAvailable,
    );

    console.log("Has all permissions", hasAllPermissions);

    if (!hasAllPermissions) {
      setShowPermissionDialog(true);
      const permissionRequests =
        await proctorManagerRef.current?.getRequestPermissions();

      let allowToStart = false;
      if (isSeb) {
        // In SEB, audio and screen are optional
        // If hasCamera is true, require camera permission
        allowToStart = checkCamera.isAvailable
          ? !!permissionRequests.camera
          : true;
      } else {
        // In non-SEB, all permissions are required
        allowToStart = checkCamera.isAvailable
          ? !!permissionRequests?.audio &&
            !!permissionRequests.camera &&
            !!permissionRequests.screen
          : !!permissionRequests?.audio && !!permissionRequests.screen;
      }

      if (!allowToStart) {
        proctorManagerRef.current = null;
        toast({
          description: "Need to provide required permissions for proctoring",
          status: "error",
        });
        return false;
      } else {
        startProctor = true;
      }

      console.log("Permission requests", permissionRequests);

      // Recheck permissions after request
      const permissionsGranted = await checkPermissions(
        isSeb,
        checkCamera.isAvailable,
        proctorManagerRef.current,
      );
      console.log("Permissions granted", permissionsGranted);
      if (!permissionsGranted) {
        return startProctor;
      } else {
        setShowPermissionDialog(false);
      }
    }

    // Initialize tracking session and violations tracker
    if (!trackingSessionRef.current) {
      trackingSessionRef.current = initTrackingSession()
        .current as TrackingSession;
    }

    if (!violationsTrackerRef.current) {
      violationsTrackerRef.current = initViolationsTracker()
        .current as ViolationsTracker;
    }

    // Step 1: Start session with proctorManagerRef
    await proctorManagerRef.current?.initializeSession(
      trackingSessionRef.current,
      violationsTrackerRef.current,
    );

    const dataStartSession =
      await proctorManagerRef.current?.startSession(startSessionParams);

    if (!dataStartSession?.id) {
      console.error("Failed to start session: No session ID returned");
      toast({
        title: "Session Error",
        description: "Failed to start proctoring session",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      return false;
    }

    // Set the session ID from the response
    setSessionId(dataStartSession.id);

    // Step 2: Initialize socket manager with the session ID
    const socketManager = initSocketManager(
      dataStartSession.id,
      socket_auth_token,
    );

    try {
      // Step 3: Connect to socket server
      await socketManager.current?.connect();
      console.log("Connected to socket server");

      // Start tracking
      await proctorManagerRef.current?.startTracking();
      setIsProctoring(true);
      startProctor = true;
    } catch (error) {
      console.error("Error in socket connection:", error);
      toast({
        title: "Connection Error",
        description: "Failed to connect to proctoring server",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      setIsProctoring(false);
      startProctor = false;
    }

    return startProctor;
  };

  const onStopProctoring = async () => {
    try {
      // First stop all tracking
      if (proctorManagerRef.current) {
        await proctorManagerRef.current?.stopTracking();
      }

      // Then try to stop socket session
      if (socketManagerRef.current) {
        try {
          await socketManagerRef.current.endSession();
          console.log("Session ended via socket");
          await socketManagerRef.current?.disconnect();
          console.log("Disconnected from socket server");
        } catch (error) {
          console.error("Error ending socket session:", error);
          socketManagerRef.current = null;
          console.log("Disconnected from socket server");
        }
      }

      // Finally end the proctor session
      if (proctorManagerRef.current) {
        await proctorManagerRef.current?.endSession();
      }

      // Clear all refs
      proctorManagerRef.current = null;
      socketManagerRef.current = null;
      trackingSessionRef.current = null;
      violationsTrackerRef.current = null;
      console.log("Cleaning up all refs");

      // Clear session ID
      setSessionId(null);

      // Clear intervals
      if (intervalIdRef.current.violationsInterval) {
        clearInterval(intervalIdRef.current.violationsInterval);
        intervalIdRef.current.violationsInterval = null;
      }
      if (intervalIdRef.current.pingInterval) {
        clearInterval(intervalIdRef.current.pingInterval);
        intervalIdRef.current.pingInterval = null;
      }

      // Remove violation listener
      removeViolationListener();

      // Clear violations data
      setViolationsData([]);

      console.log("Proctoring stopped successfully");
      setIsProctoring(false);
    } catch (error) {
      console.error("Error stopping proctoring:", error);
      // Force cleanup even if there's an error
      proctorManagerRef.current = null;
      socketManagerRef.current = null;
      trackingSessionRef.current = null;
      violationsTrackerRef.current = null;
      setSessionId(null);
      setIsProctoring(false);
    }
  };

  const onCloseDialogPerm = () => {
    setShowPermissionDialog(false);
  };

  const onGetActivityMetrics = async (
    params: IGetActivityMetricsParams,
  ): Promise<APIResponse<IActivityMetricResponse> | undefined> => {
    // Use the traditional method since socket manager doesn't have getActivityMetrics
    const activityCount =
      await proctorManagerRef.current?.getActivityMetrics(params);

    if (activityCount?.status === "OK") {
      return activityCount.data;
    }

    return undefined;
  };

  return (
    <ProctoringContext.Provider
      value={{
        proctorManagerRef,
        trackingSessionRef,
        violationsTrackerRef,
        initProctor,
        initTrackingSession,
        initViolationsTracker,
        onStartProctoring,
        onStopProctoring,
        isProctoring,
        getActivityMetrics: onGetActivityMetrics,
        onViolation,
        removeViolationListener,
        sessionId,
      }}
    >
      {children}
      {/* Permissions Dialog */}
      <CommonDialog
        open={showPermissionDialog}
        variant="default"
        header={
          <Text
            fontSize="16px"
            fontWeight="medium"
            textAlign="center"
            className="text-white"
          >
            Proctoring Permissions Required
          </Text>
        }
        handleCancel={onCloseDialogPerm}
        maxWidth={400}
        isFooter={false}
      >
        <VStack spacing={4} align="stretch" className="p-4">
          <Text fontSize="14px" textAlign="center" mb={4}>
            Please grant the following permissions to continue with the
            proctored exam:
          </Text>

          <HStack justify="space-between" align="center">
            <Text>Audio Permission</Text>
            <div
              className={`h-4 w-4 rounded-full ${
                permissionStatus.audio ? "bg-green-500" : "bg-red-500"
              }`}
            />
          </HStack>

          <HStack justify="space-between" align="center">
            <Text>Video Permission</Text>
            <div
              className={`h-4 w-4 rounded-full ${
                permissionStatus.video ? "bg-green-500" : "bg-red-500"
              }`}
            />
          </HStack>

          <HStack justify="space-between" align="center">
            <Text>Screen Permission</Text>
            <div
              className={`h-4 w-4 rounded-full ${
                permissionStatus.screen ? "bg-green-500" : "bg-red-500"
              }`}
            />
          </HStack>

          <Text fontSize="12px" textAlign="center" mt={4}>
            If permissions are denied, you will be redirected back to the exam
            details page.
          </Text>
        </VStack>
      </CommonDialog>
    </ProctoringContext.Provider>
  );
};

export default ProctoringProvider;

import React, { SetStateAction, Dispatch } from "react";

const SessionSnackbar = ({
  violationData,
  setShowIntegrityWatchDialog,
  showIntegrityWatchDialog,
}: {
  violationData: {
    violationType: string;
    violationCount: number;
  };
  setShowIntegrityWatchDialog: Dispatch<SetStateAction<boolean>>;
  showIntegrityWatchDialog: boolean;
}) => {
  return (
    <div
      className={`fixed left-[50%] z-10 flex -translate-x-[50%] gap-x-4 rounded-lg bg-[#FFDAD9] p-2 text-white transition-all duration-200 ease-linear ${showIntegrityWatchDialog ? "bottom-10" : "-bottom-96"}`}
    >
      <div className="flex flex-col items-center justify-center rounded-md bg-[#E83831] px-2">
        <p className="text-[40px] font-bold leading-none">
          {violationData.violationCount}
        </p>
        <p className="text-[8px] font-bold">Violations</p>
      </div>
      <div className="max-w-[360px]">
        <h6 className="text-sm font-extrabold text-[#991E19]">
          Exam monitoring in progress
        </h6>
        <p className="text-xs font-medium text-[#991E19]">
          Stay on camera, avoid background noise, don&apos;t switch tabs, and
          ensure you&apos;re alone. Violations may lead to penalties.
        </p>
      </div>
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        onClick={() => setShowIntegrityWatchDialog(false)}
      >
        <path
          d="M13.4099 12L17.7099 7.71C17.8982 7.5217 18.004 7.2663 18.004 7C18.004 6.7337 17.8982 6.47831 17.7099 6.29C17.5216 6.1017 17.2662 5.99591 16.9999 5.99591C16.7336 5.99591 16.4782 6.1017 16.2899 6.29L11.9999 10.59L7.70994 6.29C7.52164 6.1017 7.26624 5.99591 6.99994 5.99591C6.73364 5.99591 6.47824 6.1017 6.28994 6.29C6.10164 6.47831 5.99585 6.7337 5.99585 7C5.99585 7.2663 6.10164 7.5217 6.28994 7.71L10.5899 12L6.28994 16.29C6.19621 16.383 6.12182 16.4936 6.07105 16.6154C6.02028 16.7373 5.99414 16.868 5.99414 17C5.99414 17.132 6.02028 17.2627 6.07105 17.3846C6.12182 17.5064 6.19621 17.617 6.28994 17.71C6.3829 17.8037 6.4935 17.8781 6.61536 17.9289C6.73722 17.9797 6.86793 18.0058 6.99994 18.0058C7.13195 18.0058 7.26266 17.9797 7.38452 17.9289C7.50638 17.8781 7.61698 17.8037 7.70994 17.71L11.9999 13.41L16.2899 17.71C16.3829 17.8037 16.4935 17.8781 16.6154 17.9289C16.7372 17.9797 16.8679 18.0058 16.9999 18.0058C17.132 18.0058 17.2627 17.9797 17.3845 17.9289C17.5064 17.8781 17.617 17.8037 17.7099 17.71C17.8037 17.617 17.8781 17.5064 17.9288 17.3846C17.9796 17.2627 18.0057 17.132 18.0057 17C18.0057 16.868 17.9796 16.7373 17.9288 16.6154C17.8781 16.4936 17.8037 16.383 17.7099 16.29L13.4099 12Z"
          fill="#991E19"
        />
      </svg>
    </div>
  );
};

export default SessionSnackbar;

"use client";

import CommonPagination from "@/components/commons/CommonPagination/CommonPagination";
import { Button } from "@/components/ui/button";
import { BaseColor } from "@/constants/colors";
import { HStack, Image, Text } from "@chakra-ui/react";
import React from "react";
import { useCountdown } from "@/hooks/useCountdown";
import dayjs from "dayjs";

type ExamTimerAndPaginationProps = {
  pagesWithAnswers: number[];
  currentPage: number;
  onGoToPage: (value: number) => void;
  onSubmit: () => void;
  countdownSeconds: number;
  totalPages: number;
};
export const ExamTimerAndPagination = ({
  currentPage,
  onGoToPage,
  pagesWithAnswers,
  onSubmit,
  countdownSeconds,
  totalPages,
}: ExamTimerAndPaginationProps) => {
  const handleOnChangePage = (page: number) => {
    onGoToPage(page);
  };
  const { minutesLeft, secondsLeft, hoursLeft } = useCountdown({
    seconds: countdownSeconds,
  });
  const lastSavedTime = localStorage.getItem("lastSavedTime");
  const savedTime = lastSavedTime
    ? dayjs(lastSavedTime).format("HH:mm")
    : "00:00";

  return (
    <HStack
      bg="white"
      alignItems="center"
      justifyContent="center"
      px="30px"
      className="max-w-full flex-wrap gap-8 pb-6"
    >
      <Image alt="stopwatch" src="/images/icons/stopwatch.svg" />
      <Text
        color={BaseColor.PRIMARY}
        fontWeight="bold"
        fontSize="20px"
        mr="32px"
        className="tabular-nums"
      >
        {`${hoursLeft} : ${minutesLeft} : ${secondsLeft}`}
      </Text>

      <div className="mx-6 flex flex-1 flex-wrap items-center justify-center">
        <CommonPagination
          isSimplePaging
          totalPages={totalPages}
          handleOnChange={handleOnChangePage}
          currentPage={currentPage}
          pageSize={10}
          maxPagesToShow={15}
          selectedItemClassName="rounded-full border-[#0A3150] border-2 font-semibold text-[#0A3150]"
          unselectedItemClassName="rounded-full border-[#F0F2F4] border-2 text-[13px] font-semibold text-[#0A3150]"
          highlightedItemClassName="rounded-full border-[#0A3150] bg-[#0A3150] border-2 text-[13px] font-semibold text-white"
          highlightPages={pagesWithAnswers}
        />
      </div>

      <Text
        fontSize="14px"
        fontWeight="medium"
        color={BaseColor.PRIMARY_300}
        mr="32px"
      >
        Saved at {savedTime}
      </Text>

      <Button onClick={onSubmit}>Submit Exam</Button>
    </HStack>
  );
};

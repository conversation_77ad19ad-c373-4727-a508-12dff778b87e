"use client";

import { FC } from "react";
import { Checkbox } from "@/components/ui/checkbox";

import { ICommonCheckboxProps } from "./CommonCheckbox.d";

const CommonCheckbox: FC<ICommonCheckboxProps> = ({ label, onChange }) => {
  return (
    <div className="flex items-center space-x-2">
      <Checkbox onChange={(e) => onChange(e)} id="terms" />
      <label
        htmlFor="terms"
        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {label}
      </label>
    </div>
  );
};

export default CommonCheckbox;

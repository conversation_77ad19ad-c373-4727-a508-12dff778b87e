import { chromium, FullConfig } from "@playwright/test";
import LoginPage from "../ui/pages/login-page";
import uiPages from "../utils/ui-pages";

// const email = process.env.MIVA_ADMIN_EMAIL!;
// const password = process.env.MIVA_ADMIN_PASSWORD!;

// Load environment variables for test credentials
const email = "<EMAIL>";
const password = "Qa1@miva";

async function globalSetup(config: FullConfig) {
  const { storageState, baseURL } = config.projects[0].use;

  // Launch browser with error handling
  const browser = await chromium
    .launch({
      headless: true,
      timeout: 30000, // Increase timeout for stability
    })
    .catch((error) => {
      console.error("Failed to launch browser:", error);
      throw error;
    });

  const page = await browser.newPage();
  const loginPage = new LoginPage(page);

  try {
    // Navigate and login
    await page.goto(baseURL + uiPages.login);
    await loginPage.doLogin(email, password);
    await loginPage.checkLoggedIn();

    // Save authentication state
    await page.context().storageState({
      path: storageState as string,
    });
  } catch (error) {
    console.error("Setup failed:", error);
    throw error;
  } finally {
    await browser.close();
  }
}
export default globalSetup;

{
  "compilerOptions": {
    "incremental": true, // https://nextjs.org/docs/basic-features/typescript#incremental-type-checking
    "baseUrl": ".",
    "paths": {
      "@/components/*": ["./src/components/*"],
      "@/pages/*": ["./src/pages/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/styles/*": ["./src/styles/*"],
      "@/module/*": ["./src/module/*"],
      "@/api/*": ["./src/api/*"],
      "@/constants/*": ["./src/constants/*"],
      "@/mocks/*": ["./__mocks__/*"],
      "@/tests/*": ["./.jest/*"]
    },
    "noUnusedLocals": false,
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "declaration": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "typeRoots": ["./node_modules/@types"],
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.d.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "prettier.config.js"
  ],
  "exclude": ["node_modules", "node_modules/@iconscout/react-unicons"]
}

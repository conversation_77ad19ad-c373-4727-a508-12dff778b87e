import { FormikProps } from "formik";
import { Flex, Text, Stack } from "@chakra-ui/react";

import { IActivateAccountValue } from "./ActivateAccount";
import HeaderForm from "../components/HeaderForm";
import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";

const ActivateAccountForm = (props: FormikProps<IActivateAccountValue>) => {
  const { touched, errors, handleSubmit } = props;
  const { confirmPassword: confirmPasswordTouched, password: passwordTouched } =
    touched;
  const { confirmPassword: confirmPasswordError, password: passwordError } =
    errors;
  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Activate your account"
        subTitle="Create a password to activate your account"
      />
      <Stack mt="48px" spacing="16px">
        <InputField placeholder="Create your password" />
        {passwordTouched && passwordError && (
          <Text className="error-message text-center">{passwordError}</Text>
        )}
        <InputField placeholder="Confirm your password" />
        {confirmPasswordTouched && confirmPasswordError && (
          <Text className="error-message text-center">
            {confirmPasswordError}
          </Text>
        )}
      </Stack>
      <Stack mt="48px">
        <ButtonCTA
          variant={EButtonType.SECONDARY}
          onClick={() => handleSubmit()}
          isLoading={false}
        >
          Activate
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default ActivateAccountForm;

/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { FC, useEffect, useState } from "react";
import {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import CommonPagination from "@/components/commons/CommonPagination/CommonPagination";

import { ICommonTableProps } from "./CommonTable.d";
import CommonLoading from "../CommonLoading/CommonLoading";

const CommonTable: FC<ICommonTableProps> = ({
  dataTable = [],
  totalNumOfPages,
  columnsTable,
  rowSelection,
  setRowSelection,
  hidePagination,
  isSimplePaging,
  manualPagination = true,
  pagination,
  setPagination,
  customFooter,
  curveRowEdge = false,
  containerClassName,
  compact,
  isLoading = false,
  columnVisibility: defaultColumnVisiblity,
}) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    defaultColumnVisiblity || {},
  );
  const [defaultRowSelection, setDefaultRowSelection] = useState({});
  const [isTableLoaded, setIsTableLoaded] = useState(false);

  useEffect(() => {
    setIsTableLoaded(true);
  }, []);

  useEffect(() => {
    if (defaultColumnVisiblity) {
      setColumnVisibility(defaultColumnVisiblity);
    }
  }, [defaultColumnVisiblity]);

  const table = useReactTable({
    onColumnFiltersChange: setColumnFilters,
    data: dataTable,
    columns: columnsTable,
    getRowId: (row) => row?.selectedId || row?.id,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    ...(!hidePagination
      ? {
          manualPagination,
          onPaginationChange: setPagination,
          getPaginationRowModel: getPaginationRowModel(),
        }
      : {}),
    onRowSelectionChange: (row) => {
      if (setRowSelection) {
        setRowSelection(row);
      } else {
        setDefaultRowSelection(row);
      }
    },
    enableRowSelection: true,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      ...(rowSelection
        ? { rowSelection }
        : { rowSelection: defaultRowSelection }),
      ...(!hidePagination ? { pagination } : {}),
    },
  });

  const handleChangePageSize = (newPageSize: number) => {
    table.setPageSize(newPageSize);
  };

  const handleChangePage = (page: number) => {
    table.setPageIndex(page > 0 ? page - 1 : 0);
  };

  return (
    <div className="w-full">
      <div className="rounded-md">
        {
          <Table containerClassName={containerClassName}>
            <TableHeader className="rounded-[8px] bg-primary text-white hover:bg-primary/90">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow
                  className="rounded-[8px] border border-transparent hover:bg-primary/90"
                  key={headerGroup.id}
                >
                  {headerGroup.headers.map((header, index) => {
                    return (
                      <TableHead
                        className={cn("text-white", {
                          "rounded-bl-[8px] rounded-tl-[8px]": index === 0,
                          "rounded-br-[8px] rounded-tr-[8px]":
                            index === headerGroup.headers.length - 1,
                        })}
                        key={header.id}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            {
              <TableBody>
                {(isTableLoaded && isLoading) ||
                (!isTableLoaded && table.getRowModel().rows?.length === 0) ? (
                  <TableRow>
                    <TableCell
                      colSpan={columnsTable?.length}
                      className="h-64 text-center"
                    >
                      <div className="flex items-center justify-center">
                        <CommonLoading size="medium" />
                      </div>
                    </TableCell>
                  </TableRow>
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row, index) => (
                    <TableRow
                      className={cn(curveRowEdge && "border-none", {
                        "bg-[#F0F2F4]": index % 2,
                      })}
                      key={row.id}
                      data-state={
                        rowSelection && row.getIsSelected() && "selected"
                      }
                    >
                      {row.getVisibleCells().map((cell, cellIndex) => (
                        <TableCell
                          key={cell.id}
                          className={cn(
                            `${curveRowEdge && "h-12 py-2"} ${curveRowEdge && cellIndex === 0 && "rounded-l-lg"} ${curveRowEdge && cellIndex === row.getVisibleCells().length - 1 && "h-12 rounded-r-lg py-4"}`,
                            compact && "!p-2",
                          )}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columnsTable?.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
                {customFooter}
              </TableBody>
            }
          </Table>
        }
      </div>
      {!hidePagination &&
        pagination &&
        !!totalNumOfPages &&
        totalNumOfPages > 0 && (
          <div className="mt-[8px] space-x-2">
            <CommonPagination
              isSimplePaging={isSimplePaging}
              totalPages={totalNumOfPages ?? 0}
              handleOnChange={handleChangePage}
              currentPage={(pagination?.pageIndex || 0) + 1}
              pageSize={pagination?.pageSize || 10}
              handleChangePageSize={handleChangePageSize}
            />
          </div>
        )}
    </div>
  );
};

export default CommonTable;

import {
  Table as TableIcon,
  Plus,
  Minus,
  TableCellsMerge,
  TableColumnsSplit,
} from "lucide-react";
import { IconButton } from "../MenuBar/MenuBar";

const TableToolbar = ({ editor }: { editor: any }) => (
  <div className="mb-2 flex flex-wrap items-center gap-2 border-b border-gray-300 pb-2">
    <span className="font-semibold text-gray-700">Table Options:</span>
    <IconButton
      icon={<Plus size={16} />}
      action={() => editor.chain().focus().addColumnAfter().run()}
      title="Add Column After"
      disabled={!editor.can().addColumnAfter()}
    />
    <IconButton
      icon={<Minus size={16} />}
      action={() => editor.chain().focus().deleteColumn().run()}
      title="Delete Column"
      disabled={!editor.can().deleteColumn()}
    />
    <IconButton
      icon={<Plus size={16} />}
      action={() => editor.chain().focus().addRowAfter().run()}
      title="Add Row After"
      disabled={!editor.can().addRowAfter()}
    />
    <IconButton
      icon={<Minus size={16} />}
      action={() => editor.chain().focus().deleteRow().run()}
      title="Delete Row"
      disabled={!editor.can().deleteRow()}
    />
    <IconButton
      icon={<TableIcon size={16} />} // Reusing TableIcon for delete table
      action={() => editor.chain().focus().deleteTable().run()}
      title="Delete Table"
      disabled={!editor.can().deleteTable()}
    />
    <IconButton
      icon={<TableCellsMerge size={16} />}
      action={() => editor.chain().focus().mergeCells().run()}
      disabled={!editor.can().mergeCells()}
      title="Merge cells"
    />
    <IconButton
      icon={<TableColumnsSplit size={16} />}
      action={() => editor.chain().focus().splitCell().run()}
      disabled={!editor.can().splitCell()}
      title="Split cell"
    />
  </div>
);

export default TableToolbar;

import { APIResponse } from "@/api/config/api.d";
import { getQuestions } from "@/api/repository/exam-session/get-questions";
import {
  ExamQuestionsRequestType,
  SingleQuestionType,
} from "@/api/repository/exam-session/get-questions.d";
import { queryClient } from "@/components/providers/MainProvider";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useEffect } from "react";

export const QUERY_KEY_GET_EXAM_QUESTION = "Get_Exam_Question";

export const useGetAllQuestions = ({
  exam_id,
  session_id,
}: ExamQuestionsRequestType) => {
  useEffect(() => {
    return () => {
      queryClient.removeQueries({
        queryKey: [QUERY_KEY_GET_EXAM_QUESTION],
      });
    };
  }, [queryClient]);

  const query = useQuery({
    queryKey: [
      QUERY_KEY_GET_EXAM_QUESTION,
      {
        exam_id,
        session_id,
      },
    ],
    queryFn: () => getQuestions({ exam_id, session_id }),
    retry: false,
    placeholderData: keepPreviousData,
    enabled: !!exam_id && !!session_id,
    refetchOnWindowFocus: false,
    select: (response: APIResponse<SingleQuestionType[]>) => {
      return response.data;
    },
  });
  return {
    ...query,
  };
};

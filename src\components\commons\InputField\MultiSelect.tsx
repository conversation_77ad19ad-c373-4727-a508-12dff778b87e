import React from "react";
import { Check, ChevronsUpDown, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

type Option = {
  value: string;
  label: string;
  key?: string;
};

type MultiSelectProps = {
  options?: Option[];
  selected?: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
};

const MultiSelect = ({
  options = [],
  selected = [],
  onChange,
  placeholder = "Select items...",
}: MultiSelectProps) => {
  const [open, setOpen] = React.useState(false);
  const triggerRef = React.useRef<HTMLButtonElement>(null);

  // Ensure we're working with arrays even if undefined is passed
  const safeOptions = React.useMemo(
    () => (Array.isArray(options) ? options : []),
    [options],
  );
  const safeSelected = React.useMemo(
    () => (Array.isArray(selected) ? selected : []),
    [selected],
  );

  const selectedItems = React.useMemo(
    () => safeOptions.filter((option) => safeSelected.includes(option.value)),
    [safeOptions, safeSelected],
  );

  const toggleOption = (value: string) => {
    const updatedSelected = safeSelected.includes(value)
      ? safeSelected.filter((item) => item !== value)
      : [...safeSelected, value];
    onChange(updatedSelected);
  };

  const removeItem = (value: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const updatedSelected = safeSelected.filter((item) => item !== value);
    onChange(updatedSelected);
  };

  return (
    <div className="flex flex-col gap-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            ref={triggerRef}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="h-12 w-full justify-between"
          >
            <span className="truncate">
              {selectedItems.length > 0
                ? `${selectedItems.length} selected`
                : placeholder}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-full p-0"
          align="start"
          style={{ width: triggerRef.current?.offsetWidth }}
        >
          <div className="max-h-72 overflow-auto p-1">
            {safeOptions.map((option) => {
              const isSelected = safeSelected.includes(option.value);
              return (
                <div
                  key={option.key || option.value}
                  className={`relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100 ${isSelected ? "bg-gray-100" : ""} `}
                  onClick={() => toggleOption(option.value)}
                >
                  <Check
                    className={`mr-2 h-4 w-4 ${
                      isSelected ? "opacity-100" : "opacity-0"
                    }`}
                  />
                  {option.label}
                </div>
              );
            })}
          </div>
        </PopoverContent>
      </Popover>

      {selectedItems.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedItems.map((item) => (
            <div
              key={item.value}
              className="flex w-48 items-center justify-between gap-1 rounded bg-[#E7EAEE] py-1.5 pl-3 pr-2.5 text-xs font-medium"
            >
              <span className="w-44 truncate">{item.label}</span>
              <button
                onClick={(e) => removeItem(item.value, e)}
                className="ml-1 rounded-full p-0.5 hover:bg-gray-200"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MultiSelect;

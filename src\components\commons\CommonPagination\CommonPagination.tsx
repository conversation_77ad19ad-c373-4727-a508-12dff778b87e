import { FC, useMemo } from "react";

import {
  Pagin<PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import CommonSelect from "@/components/commons/CommonSelect/CommonSelect";
import { OPTION_PAGE_SIZE } from "@/constants/commons";

interface ICommonPaginationProps {
  totalPages: number;
  currentPage: number;
  handleOnChange: (page: number) => void;
  maxPagesToShow?: number;
  pageSize?: number;
  isSimplePaging?: boolean;
  handleChangePageSize?: (size: number) => void;
  selectedItemClassName?: string;
  unselectedItemClassName?: string;
  highlightPages?: number[];
  highlightedItemClassName?: string;
}

const CommonPagination: FC<ICommonPaginationProps> = ({
  totalPages,
  handleOnChange,
  currentPage,
  maxPagesToShow = 3,
  pageSize,
  handleChangePageSize,
  isSimplePaging,
  selectedItemClassName = "",
  unselectedItemClassName = "",
  highlightPages = [],
  highlightedItemClassName = "",
}) => {
  const getPageNumbers = useMemo(() => {
    const pages: (number | string)[] = [];

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = endPage - maxPagesToShow + 1;
      }

      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push("...");
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push("...");
        }
        pages.push(totalPages);
      }
    }

    return pages;
  }, [totalPages, currentPage, maxPagesToShow]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleNext = (e: any) => {
    e.preventDefault();
    if (currentPage < totalPages) {
      handleOnChange(currentPage + 1);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handlePrev = (e: any) => {
    e.preventDefault();
    if (currentPage > 1) {
      handleOnChange(currentPage - 1);
    }
  };
  return (
    <Pagination className="flex flex-wrap items-center justify-center">
      <PaginationContent>
        <PaginationItem className="cursor-pointer">
          <PaginationPrevious
            isSimplePaging={isSimplePaging}
            onClick={handlePrev}
          />
        </PaginationItem>
        {getPageNumbers.map((number, index) =>
          typeof number === "number" ? (
            <PaginationItem
              key={index}
              className="flex cursor-pointer flex-wrap items-center"
            >
              <PaginationLink
                onClick={() => handleOnChange(number as number)}
                isActive={currentPage === number}
                className={
                  highlightPages.includes(number)
                    ? highlightedItemClassName
                    : currentPage === number
                      ? selectedItemClassName
                      : unselectedItemClassName
                }
              >
                {number}
              </PaginationLink>
            </PaginationItem>
          ) : (
            <PaginationItem key={index} className="cursor-pointer">
              <PaginationEllipsis />
            </PaginationItem>
          ),
        )}
        <PaginationItem className="cursor-pointer">
          <PaginationNext
            isSimplePaging={isSimplePaging}
            onClick={handleNext}
          />
        </PaginationItem>
      </PaginationContent>
      {!!pageSize && !isSimplePaging && (
        <div className="flex w-[180px] items-center gap-[8px]">
          <div>Show</div>
          <CommonSelect
            className="w-fit max-w-[55px]"
            showIndicatorIcon={false}
            value={pageSize}
            onChange={(pageSize) =>
              handleChangePageSize && handleChangePageSize(Number(pageSize))
            }
            options={OPTION_PAGE_SIZE}
          />
          <div>per page</div>
        </div>
      )}
    </Pagination>
  );
};

export default CommonPagination;

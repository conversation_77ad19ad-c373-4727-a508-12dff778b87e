import CryptoJS from "crypto-js";

const SECRET_KEY = "MIVA_SECRET_KEY";

export interface EncryptedData {
  keyId: string;
  randomAccessKey: string;
}

export class Encryption {
  static encrypt(value: any): string {
    return CryptoJS.AES.encrypt(
      JSON.stringify(value),
      SECRET_KEY as string,
    ).toString();
  }

  static decrypt(value: string): string {
    return CryptoJS.AES.decrypt(value, SECRET_KEY as string).toString(
      CryptoJS.enc.Utf8,
    );
  }

  static encryptDataWithAccessKey(data: EncryptedData): string {
    const payloadStr = JSON.stringify(data);
    return CryptoJS.AES.encrypt(payloadStr, SECRET_KEY as string).toString();
  }

  static decryptDataWithAccessKey(value: string): EncryptedData {
    const decrypted = CryptoJS.AES.decrypt(
      value,
      SECRET_KEY as string,
    ).toString(CryptoJS.enc.Utf8);
    return JSON.parse(decrypted);
  }
}

import { BaseColor } from "@/constants/colors";
import {
  Box,
  Collapse,
  Input,
  InputGroup,
  Text,
  useDisclosure,
} from "@chakra-ui/react";
import { AutocompleteProps } from "./Autocomplete.d";

export const Autocomplete = ({
  value,
  onChange,
  options,
  placeholder,
  fontSize,
  label,
  required,
  size = "lg",
}: AutocompleteProps) => {
  const { isOpen, onToggle } = useDisclosure();

  return (
    <div>
      {!!label && (
        <Text
          mb="8px"
          fontWeight="600"
          color="#0A3150"
          fontSize={fontSize || "14px"}
        >
          {label}{" "}
          {required && (
            <Text as="span" color="red.500">
              *
            </Text>
          )}
        </Text>
      )}
      <Box position="relative" zIndex={1}>
        <InputGroup mt={2} size={size}>
          <Input
            type="text"
            placeholder={placeholder}
            onFocus={() => onToggle()}
            onBlur={() => onToggle()}
            value={value}
            onChange={onChange}
            fontSize={fontSize || "14px"}
          />
        </InputGroup>
        <Collapse in={isOpen}>
          <Box
            position={"absolute"}
            zIndex={200}
            mt="2"
            right="70px"
            left={0}
            rounded="md"
            shadow="md"
            border="1px solid #E7EAEE"
            maxHeight={200}
            overflowY="auto"
            bg="white"
          >
            {options.map((option) => {
              return (
                <Text
                  px={4}
                  py={2}
                  cursor={"pointer"}
                  key={option}
                  color={BaseColor.PRIMARY}
                  fontSize="13px"
                  _hover={{ background: "#E7EAEE" }}
                  fontWeight={500}
                  onClick={() => {
                    onChange({ target: { value: option } });
                  }}
                  textTransform="capitalize"
                >
                  {option}
                </Text>
              );
            })}
          </Box>
        </Collapse>
      </Box>
    </div>
  );
};

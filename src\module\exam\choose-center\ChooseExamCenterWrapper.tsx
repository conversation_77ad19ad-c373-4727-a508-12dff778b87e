"use client";

import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import CommonDialog from "@/components/commons/CommonDialog/CommonDialog";
import CommonSelect from "@/components/commons/CommonSelect/CommonSelect";
import { mockExamCentres, mockStateOptions } from "@/utils/commons";
import { Stack, Text } from "@chakra-ui/react";
import { useRouter } from "next/dist/client/components/navigation";
import Image from "next/image";
import React, { useState } from "react";

const ChooseExamCenterWrapper = () => {
  const [state, setState] = useState<string | undefined>(
    mockStateOptions[0].value,
  );
  const [examCentre, setExamCentre] = useState<string | undefined>(
    mockExamCentres[0].value,
  );
  const [openConfirmation, setOpenConfirmation] = useState(false);

  const onChangeStateOptions = (value: string) => {
    setState(value);
  };

  const onChangeExamCentre = (value: string) => {
    setExamCentre(value);
  };

  const router = useRouter();

  return (
    <div className="px-auto mx-auto mt-[60px] flex h-full w-[400px] flex-col items-center gap-6">
      <Text fontWeight="bold" fontSize="32px" lineHeight="40px">
        Choose Your Exam Centre
      </Text>
      <Text fontSize="16px" fontWeight="medium">
        Pick the perfect spot to take your exams!
      </Text>

      <Stack className="w-full">
        <Text fontWeight="semibold" fontSize="14px">
          State
        </Text>
        <CommonSelect
          label=""
          placeholder="State"
          value={state}
          showIndicatorIcon={true}
          options={mockStateOptions}
          className="bg-white shadow-none focus:!ring-0"
          onChange={onChangeStateOptions}
        />
      </Stack>

      <Stack className="w-full">
        <Text fontWeight="semibold" fontSize="14px">
          Exam Centre
        </Text>
        <CommonSelect
          label=""
          placeholder="Exam Centre"
          value={examCentre}
          showIndicatorIcon={true}
          options={mockExamCentres}
          className="bg-white shadow-none focus:!ring-0"
          onChange={onChangeExamCentre}
        />
      </Stack>

      <ButtonCTA
        onClick={() => {
          // router.push("/exam/management");
          setOpenConfirmation(true);
        }}
        className="mt-8 w-full"
      >
        Save
      </ButtonCTA>
      <CommonDialog
        header="Attention!"
        open={openConfirmation}
        handleCancel={() => setOpenConfirmation(false)}
      >
        <Text className="my-4 flex items-end gap-2 text-3xl font-bold text-[#0A3150]">
          <span className="text-xl"></span>Miva flagship centre
        </Text>
        <p className="my-2 flex items-end gap-2 px-1 text-lg font-medium text-[#AA9074]">
          1059 O.P. Fingesi Road, Mabushi, Abuja 900108, Federal Capital
          Territory
        </p>
        <div className="my-2 flex items-center gap-3 rounded-lg bg-[#FDEBEA] px-2 py-1">
          <Image
            src={"/images/icons/info.svg"}
            alt="info"
            width={24}
            height={24}
          />
          <Text className="text-sm font-medium text-[#E83831]">
            Please confirm your selected exam centre.
          </Text>
        </div>
        <div className="flex w-full items-center justify-end gap-4">
          <div className="my-3 flex items-center gap-6 justify-self-end">
            <ButtonCTA
              onClick={() => {
                setOpenConfirmation(false);
              }}
              className="w-full"
              variant={EButtonType.OUTLINE}
            >
              Cancel
            </ButtonCTA>
            <ButtonCTA
              onClick={() => {
                router.push("/exam/management");
              }}
              className="w-full"
            >
              Yes, Save
            </ButtonCTA>
          </div>
        </div>
      </CommonDialog>
    </div>
  );
};

export default ChooseExamCenterWrapper;

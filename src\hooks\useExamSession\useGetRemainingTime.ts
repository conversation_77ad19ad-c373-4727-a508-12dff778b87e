import { APIResponse } from "@/api/config/api.d";
import { getRemainingExamTime } from "@/api/repository/exam-session/get-remaining-time";
import {
  ExamQuestionsRemainigtimeRequestType,
  ExamQuestionsRemainingTimeResponseType,
} from "@/api/repository/exam-session/get-remaining-time.d";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";

export const QUERY_KEY_GET_EXAM_REMAINING_TIME = "Get Exam Remaining time";

export const useGetRemainingExamTime = ({
  exam_id,
}: ExamQuestionsRemainigtimeRequestType) => {
  const [initialFetchTime, setInitialFetchTime] = useState<number | null>(null);

  const query = useQuery({
    queryKey: [
      QUERY_KEY_GET_EXAM_REMAINING_TIME,
      {
        exam_id,
      },
    ],
    queryFn: () => getRemainingExamTime({ exam_id }),
    retry: false,
    placeholderData: keepPreviousData,
    staleTime: Infinity,
    enabled: !!exam_id && initialFetchTime === null,
    refetchOnWindowFocus: false,
    select: (response: APIResponse<ExamQuestionsRemainingTimeResponseType>) => {
      const { remaining_time, remaining_minutes, remaining_seconds } =
        response.data;
      return {
        remaining_time,
        remaining_minutes,
        remaining_seconds,
      };
    },
  });

  useEffect(() => {
    if (query.isSuccess && initialFetchTime === null) {
      setInitialFetchTime(Date.now());
    }
  }, [query.isSuccess, initialFetchTime]);

  useEffect(() => {
    if (initialFetchTime !== null) {
      const timeoutId = setTimeout(() => {
        query.refetch();
      }, 60000);

      return () => clearTimeout(timeoutId);
    }
  }, [initialFetchTime, query]);

  return {
    ...query,
  };
};

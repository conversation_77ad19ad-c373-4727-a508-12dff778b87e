import { InputProps } from "@chakra-ui/react";
import { ReactNode } from "react";

export type CommonSearchWithInfiniteScrollProps = Pick<
  InputProps,
  "isDisabled"
> & {
  setSearch: (search: string) => void;
  search?: string;
  children: ReactNode;
  type?: string;
  dataLength: number;
  hasMore: boolean;
  loadMore: () => void;
  placeholder: string;
  fontSize?: string;
  selected?: string;
  handleClear?: () => void;
};

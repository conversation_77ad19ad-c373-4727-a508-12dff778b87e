import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import {
  ExamQuestionsRequestType,
  SingleQuestionType,
} from "./get-questions.d";
import { APIResponse } from "@/api/config/api.d";

export const getQuestions = async ({
  exam_id,
  session_id,
}: ExamQuestionsRequestType): Promise<APIResponse<SingleQuestionType[]>> => {
  try {
    const response = await baseApi.get(
      `/${Routes[MODULE_ROUTE.STUDENT].QUESTIONS}/${exam_id}/sessions/${session_id}/questions`,
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

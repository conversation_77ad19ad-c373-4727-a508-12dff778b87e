"use client";

import Editor from "@/components/commons/Editor/Editor";
import { BaseColor } from "@/constants/colors";
import { Box, HStack, Spacer, Text, Textarea, VStack } from "@chakra-ui/react";
import React from "react";
import { InfoOutlineIcon } from "@chakra-ui/icons";
import { Button } from "@chakra-ui/react";

type EssayQuestionProps = {
  answerText: string;
  onAnswerUpdate: (value: string) => void;
  options?: {
    id: string;
    value: string;
  }[];
  numberOfAcceptedWord: number;
  exam_id: string;
  session_id: string;
  question_id: string;
};

export const EssayQuestion = ({
  answerText,
  onAnswerUpdate,
  options,
  numberOfAcceptedWord,
}: EssayQuestionProps) => {
  const [wordCount, setWordCount] = React.useState(0);

  React.useEffect(() => {
    const words = answerText.trim().split(/\s+/).filter(Boolean);
    setWordCount(words.length);
  }, [answerText]);

  return (
    <VStack width="100%" alignItems="start" mb="64px" className="p-8">
      {options && options.length > 0 && (
        <Box mb="4">
          {/* <Text fontWeight="bold" fontSize="14px" color={BaseColor.PRIMARY}>
            Additional Instructions
          </Text> */}
          {options.map((opt) => (
            <Text key={opt.id} fontSize="14px">
              {opt.value}
            </Text>
          ))}
        </Box>
      )}

      <HStack width="100%" alignItems="center" mb="2">
        <Text fontWeight="semibold" fontSize="14px" color={BaseColor.PRIMARY}>
          Type answer
        </Text>
        <InfoOutlineIcon color="#BDC2C9" boxSize={4} ml={2} />
        <Text fontSize="14px" color="#3B5A73" ml={1}>
          Type a &quot;$&quot; at the <b>start</b> and <b>end</b> of your
          equation to format correctly.
        </Text>
      </HStack>

      <div className="max-[1200px] w-full rounded-lg bg-white">
        <Editor value={answerText} onChange={onAnswerUpdate} />
      </div>

      <HStack width="100%" alignItems="center" mt="2">
        <Button
          leftIcon={<InfoOutlineIcon />}
          size="sm"
          variant="outline"
          colorScheme="#BDC2C9"
        >
          Hint
        </Button>
        <Text fontSize="14px" color="#3B5A73" ml={2}>
          Type a &quot;$&quot; at the <b>start</b> and <b>end</b> of your
          equation to format correctly.
        </Text>
        <Spacer />
        <Text fontWeight="medium" fontSize="14px" color={BaseColor.PRIMARY_300}>
          {wordCount} / {numberOfAcceptedWord}
        </Text>
      </HStack>
    </VStack>
  );
};

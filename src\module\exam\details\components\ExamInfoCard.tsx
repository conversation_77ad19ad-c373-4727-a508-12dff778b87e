import { Card } from "@/components/ui/card";
import { BaseColor } from "@/constants/colors";
import { GridItem, VStack, Text, HStack } from "@chakra-ui/react";
import React from "react";
import { ExamInfoCardProps } from "../ExamDetails";
import Image from "next/image";

const ExamInfoCard = ({
  title,
  subtitle,
  value,
  icon,
  key,
}: ExamInfoCardProps) => {
  return (
    <GridItem w="100%" h="120px" key={key}>
      <Card className="flex flex-col gap-3.5 rounded-[12px] p-5">
        <VStack gap={0} className="w-full items-start" alignItems="start">
          <Text
            fontWeight="medium"
            fontSize="12px"
            color={BaseColor.PRIMARY_300}
          >
            {title}
          </Text>
          <Text
            fontWeight="medium"
            fontSize="12px"
            color={BaseColor.PRIMARY_300}
          >
            {subtitle}
          </Text>
        </VStack>
        <HStack justifyContent="space-between">
          <Text fontWeight="bold" fontSize="24px">
            {value}
          </Text>
          <Image src={icon} alt="icon" width={24} height={24} />
        </HStack>
      </Card>
    </GridItem>
  );
};

export default ExamInfoCard;

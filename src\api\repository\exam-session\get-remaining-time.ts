import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import {
  ExamQuestionsRemainigtimeRequestType,
  ExamQuestionsRemainingTimeResponseType,
} from "./get-remaining-time.d";
import { APIResponse } from "@/api/config/api.d";

export const getRemainingExamTime = async ({
  exam_id,
}: ExamQuestionsRemainigtimeRequestType): Promise<
  APIResponse<ExamQuestionsRemainingTimeResponseType>
> => {
  try {
    const response = await baseApi.get(
      `/${Routes[MODULE_ROUTE.STUDENT].QUESTIONS}/${exam_id}/remaining-time`,
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

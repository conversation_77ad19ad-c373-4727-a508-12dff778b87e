import { APIRequestContext, APIResponse } from "@playwright/test";

export async function executeRequest(
  apiContext: APIRequestContext,
  requestUrl: string,
  method: string,
  requestOptions?: object,
) {
  try {
    let response: APIResponse;

    switch (method.toLowerCase()) {
      case "get":
        response = await apiContext.get(requestUrl, requestOptions);
        break;
      case "post":
        response = await apiContext.post(requestUrl, requestOptions);
        break;
      case "patch":
        response = await apiContext.patch(requestUrl, requestOptions);
        break;
      case "put":
        response = await apiContext.put(requestUrl, requestOptions);
        break;
      case "delete":
        response = await apiContext.delete(requestUrl, requestOptions);
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
    const responseCode = response.status();
    const responseOk = response.ok();

    if (!responseOk) {
      const errorStatus = `Code: ${responseCode} \r\n`;
      const responseStatus = `Status: ${responseOk} \r\n`;
      const errorResponse = `Response: ${await response.text()} \r\n`;
      throw `${errorStatus} ${errorResponse} ${responseStatus} `;
    }
    return response;
  } catch (error) {
    const errorRequestUrl = `Request url: ${requestUrl} \r\n`;
    const errorRequestMethod = `Method: ${method} \r\n`;
    const errorRequestOptions = `Request options: ${JSON.stringify(requestOptions)} \r\n`;

    throw new Error(
      `Invalid request! Failed on 'executeRequest' method. \r\n ${errorRequestUrl} ${errorRequestMethod} ${errorRequestOptions} ${error}`,
    );
  }
}

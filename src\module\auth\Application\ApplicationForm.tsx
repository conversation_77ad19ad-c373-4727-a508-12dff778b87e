import React from "react";
import { FormikProps } from "formik";
import {
  Text,
  Grid,
  Box,
  UnorderedList,
  ListItem,
  Image,
  Checkbox,
  Stack,
} from "@chakra-ui/react";
import { IApplicationFormValue } from "./Application";

// import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";
import PhoneInputFlag from "@/components/commons/InputField/PhoneInputFlag";
import SelectInput from "@/components/commons/InputField/SelectInput";
import DatePickerInput from "@/components/commons/InputField/DateInput";
import { Label } from "recharts";

const ApplicationForm = (props: FormikProps<IApplicationFormValue>) => {
  const { touched, errors } = props;
  const [gender, setGender] = React.useState("");
  const [admissionType, setAdmissionType] = React.useState("");
  const {
    firstName: firstNameTouched,
    lastName: lastNameTouched,
    emailAddress: emailAddressTouched,
    nationality: nationalityTouched,
    residentialAddress: residentialAddressTocuhed,
  } = touched;
  const {
    firstName: firstNameError,
    lastName: lastNameError,
    phoneNumber: phoneNumberError,
    emailAddress: emailAddressError,
    nationality: nationalityError,
    residentialAddress: residentialAddressError,
  } = errors;

  const programmes = [
    {
      label: "Accounting",
      value: "",
    },
    {
      label: "Business Administration",
      value: "",
    },
    {
      label: "Computer science",
      value: "",
    },
  ];

  const countries = [
    {
      label: "",
      value: "",
    },
    {
      label: "Nigeria",
      value: "",
    },
    {
      label: "Ghana",
      value: "",
    },
    {
      label: "Liberia",
      value: "",
    },
    {
      label: "Sierra Leone",
      value: "",
    },
  ];

  const states = [
    {
      label: "",
      value: "",
      country: "",
    },
    {
      label: "Lagos",
      value: "Lagos",
      country: "Nigeria",
    },
    {
      label: "Abuja",
      value: "Abuja",
      country: "Nigeria",
    },
    {
      label: "Ibadan",
      value: "Ibadan",
      country: "Nigeria",
    },
    {
      label: "Port Harcourt",
      value: "Port Harcourt",
      country: "Ghana",
    },
    {
      label: "Accra",
      value: "Accra",
      country: "Ghana",
    },
    {
      label: "Kumasi",
      value: "Kumasi",
      country: "Ghana",
    },
    {
      label: "Monrovia",
      value: "Monrovia",
      country: "Liberia",
    },
    {
      label: "Bonga",
      value: "Bonga",
      country: "Liberia",
    },
    {
      label: "Sao Tome",
      value: "Sao Tome",
      country: "Sierra Leone",
    },
    {
      label: "Freetown",
      value: "Freetown",
      country: "Sierra Leone",
    },
  ];

  const handleDateSelect = (val: string) => {
    console.log("val", val);
  };

  const setSelectedOption = (val: string) => {
    setGender(val);
  };

  const setAdmissionOption = (val: string) => {
    setAdmissionType(val);
  };

  const selectFile = (val: object) => {
    console.log("val", val);
  };

  return (
    <div className="mt-6">
      <h4 className="pb-6 text-lg font-bold">Personal Information</h4>
      <Grid templateColumns="repeat(2, 1fr)" gap={6}>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            First Name
          </Text>
          <InputField placeholder="" />
          {firstNameTouched && firstNameError && (
            <Text className="error-message text-center">{firstNameError}</Text>
          )}
        </div>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Last Name
          </Text>
          <InputField placeholder="" />
          {lastNameTouched && lastNameError && (
            <Text className="error-message text-center">{lastNameError}</Text>
          )}
        </div>
      </Grid>
      <Grid templateColumns="repeat(2, 1fr)" gap={6} marginY={"24px"}>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Phone Number
          </Text>
          <PhoneInputFlag
            label={""}
            error={phoneNumberError}
            onChange={(_phone, _country) => {
              console.log("_phone", _phone);
              console.log("_country", _country);
            }}
          />
        </div>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Email Address
          </Text>
          <InputField placeholder="" />
          {emailAddressTouched && emailAddressError && (
            <Text className="error-message text-center">
              {emailAddressError}
            </Text>
          )}
        </div>
      </Grid>
      <Grid templateColumns="repeat(2, 1fr)" gap={6} marginY={"24px"}>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Programme
          </Text>
          <SelectInput options={programmes} />
        </div>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Date of Birth
          </Text>
          <DatePickerInput />
        </div>
      </Grid>
      <Grid templateColumns="repeat(2, 1fr)" gap={6} marginY={"24px"}>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Gender
          </Text>
          <div className="flex items-center gap-10">
            <Box
              className="w-full border border-[#0A3150]"
              borderRadius={"8px"}
              paddingX={"18px"}
              paddingY={"12px"}
              display={"flex"}
              border={
                gender === "male" ? "1px solid #0A3150" : "1px solid #CED6DC"
              }
              background={gender === "male" ? "#DDE2E7" : "white"}
              fontWeight={gender === "male" ? 600 : 400}
              gap={2}
            >
              <input
                type="radio"
                name="radio_1"
                value="male"
                checked={gender === "male"}
                onChange={(e) => setSelectedOption(e.target.value)}
              />
              <Text color={"#0A3150"} fontSize={"14px"}>
                Male
              </Text>
            </Box>
            <Box
              borderRadius={"8px"}
              paddingX={"18px"}
              paddingY={"12px"}
              display={"flex"}
              border={
                gender === "female" ? "1px solid #0A3150" : "1px solid #CED6DC"
              }
              background={gender === "female" ? "#DDE2E7" : "white"}
              width={"100%"}
              fontWeight={gender === "female" ? 600 : 400}
              gap={2}
            >
              <input
                type="radio"
                name="radio_1"
                value="female"
                checked={gender === "female"}
                onChange={(e) => setSelectedOption(e.target.value)}
              />
              <Text color={"#0A3150"} fontSize={"14px"}>
                Female
              </Text>
            </Box>
          </div>
        </div>
        <div>
          <Text
            fontSize={14}
            fontWeight={600}
            color={"#0A3150"}
            paddingBottom={"8px"}
          >
            Nationality
          </Text>
          <InputField placeholder="" />
          {nationalityTouched && nationalityError && (
            <Text className="error-message text-center">
              {nationalityError}
            </Text>
          )}
        </div>
      </Grid>
      <div className="mt-12">
        <h4 className="pb-6 text-lg font-bold">Contact Information</h4>
        <div>
          <div>
            <Text
              fontSize={14}
              fontWeight={600}
              color={"#0A3150"}
              paddingBottom={"8px"}
            >
              Residential Address
            </Text>
            <InputField placeholder="" />
            {residentialAddressTocuhed && residentialAddressError && (
              <Text className="error-message text-center">
                {residentialAddressError}
              </Text>
            )}
          </div>
          <Grid templateColumns="repeat(2, 1fr)" gap={6} marginY={"24px"}>
            <div>
              <Text
                fontSize={14}
                fontWeight={600}
                color={"#0A3150"}
                paddingBottom={"8px"}
              >
                Country
              </Text>
              <SelectInput options={countries} />
            </div>
            <div>
              <Text
                fontSize={14}
                fontWeight={600}
                color={"#0A3150"}
                paddingBottom={"8px"}
              >
                State
              </Text>
              <SelectInput options={states} />
            </div>
          </Grid>
        </div>
      </div>
      <div className="mt-12">
        <h4 className="pb-6 text-lg font-bold">Documents</h4>
        <div className="flex items-center gap-10">
          <Box
            className="w-full border border-[#0A3150]"
            borderRadius={"8px"}
            paddingX={"18px"}
            paddingY={"12px"}
            display={"flex"}
            border={
              admissionType === "100"
                ? "1px solid #0A3150"
                : "1px solid #CED6DC"
            }
            background={admissionType === "100" ? "#DDE2E7" : "white"}
            fontWeight={admissionType === "100" ? 600 : 400}
            gap={2}
          >
            <input
              type="radio"
              name="radio_2"
              value="100"
              checked={admissionType === "100"}
              onChange={(e) => setAdmissionOption(e.target.value)}
            />
            <Text color={"#0A3150"} fontSize={"14px"}>
              100 Level Admission
            </Text>
          </Box>
          <Box
            borderRadius={"8px"}
            paddingX={"18px"}
            paddingY={"12px"}
            display={"flex"}
            border={
              admissionType === "direct"
                ? "1px solid #0A3150"
                : "1px solid #CED6DC"
            }
            background={admissionType === "direct" ? "#DDE2E7" : "white"}
            width={"100%"}
            fontWeight={admissionType === "direct" ? 600 : 400}
            gap={2}
          >
            <input
              type="radio"
              name="radio_2"
              value="direct"
              checked={admissionType === "direct"}
              onChange={(e) => setAdmissionOption(e.target.value)}
            />
            <Text color={"#0A3150"} fontSize={"14px"}>
              Direct Entry Admission
            </Text>
          </Box>
        </div>
        <Box
          border={"1px solid #E83831"}
          marginTop={"24px"}
          padding={"24px"}
          background={"#FDEBEA"}
          borderRadius={"8px"}
          color={"#0A3150"}
        >
          <Text fontWeight={700} fontSize={"21px"} color={"#0A3150"}>
            100 level admission requirements for BSc. in Accounting
          </Text>
          <Box marginTop={"24px"}>
            <Text fontSize={"14px"}>
              A copy of your A’ Level or O’Level result
            </Text>
            <Text fontSize={"14px"}>
              The result must include a minimum of five credits in the following
              subjects in not more than two sittings:
            </Text>
            <UnorderedList paddingLeft={"10px"} marginTop={1}>
              <ListItem fontSize={"14px"}>English Language</ListItem>
              <ListItem fontSize={"14px"}>Mathematics</ListItem>
              <ListItem fontSize={"14px"}>Economics</ListItem>
              <ListItem fontSize={"14px"}>
                Two other social science-related subjects
              </ListItem>
            </UnorderedList>
            <Text fontSize={"14px"} marginTop={1}>
              Please note that submission of Joint Admissions and Matriculation
              Board (JAMB) results is NOT MANDATORY at this stage. However, upon
              admission to the university, the provided results will be
              thoroughly verified for authenticity and compliance with the
              stated criteria, including JAMB Regularisation.
            </Text>
          </Box>
        </Box>
      </div>
      <div>
        <Text className="text-sm font-bold" marginTop={"24px"}>
          Upload your A Levels or O Level results
        </Text>
        <Box
          display={"flex"}
          justifyContent={"center"}
          alignItems={"center"}
          paddingY={"32px"}
          marginTop={"16px"}
          border={"1px solid #EEF1F3"}
          paddingX={"24px"}
          flexDirection={"column"}
        >
          <Image
            src="./images/icons/upload.svg"
            alt="upload-img"
            width={"40px"}
          />
          <Text color={"#667085"} fontWeight={500} fontSize={"14px"}>
            or Drag and drop here or{" "}
            <span
              onClick={(e) => selectFile(e)}
              style={{ color: "red", fontWeight: "bold", cursor: "pointer" }}
            >
              browse
            </span>
          </Text>
          <Text fontSize={"14px"} color={"#667085"} fontWeight={500}>
            JPG or PDF (max. 50MB)
          </Text>
        </Box>
        <div>
          <Checkbox
            colorScheme=""
            marginTop={"48px"}
            fontSize={"10px"}
            fontWeight={500}
          >
            I hereby accept the institutions{" "}
            <span style={{ color: "red", cursor: "pointer" }}>
              Terms and Conditions
            </span>
          </Checkbox>
        </div>
        <Stack marginTop={"48px"} width={"98px"}>
          {/* <ButtonCTA onClick={() => handleSubmit()}>Submit</ButtonCTA> */}
        </Stack>
      </div>
    </div>
  );
};

export default ApplicationForm;

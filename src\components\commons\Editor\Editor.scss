.custom-editor{
  &.view-editor{
    .tiptap {
      min-height: unset;
      height: fit-content;
      border: unset;
      padding: 0;
    }
  }
  .tiptap {
    min-height: 200px;
    border: 1px solid #ccc;
    padding: 1rem;
    border-radius: 0.25rem;

    &:focus {
      outline: none;
      border-color: #3b82f6; // Blue-500 equivalent
    }

    // Tiptap default styles for content
    p {
      margin-bottom: 0.5rem;
    }

    h1, h2, h3 {
      margin-top: 1rem;
      margin-bottom: 0.5rem;
      font-weight: bold;
    }

    h1 {
      font-size: 2rem;
    }
    h2 {
      font-size: 1.5rem;
    }
    h3 {
      font-size: 1.25rem;
    }

    ul, ol {
      padding-left: 1.5rem;
      margin-bottom: 0.5rem;
    }
    ul {
      list-style-type: disc;

      li {
        list-style: inherit;
        display: list-item;
        text-align: -webkit-match-parent;

        ul {
          list-style-type: circle;

          li {
            list-style: inherit;

            ul {
              list-style-type: square;

              li {
                list-style: inherit;
              }
            }
          }
        }
      }
    }

    ol {
      list-style-type: revert;
      padding: 0 1rem;
      margin: 1.25rem 1rem 1.25rem 0.4rem;

      li p {
        margin-top: 0.25em;
        margin-bottom: 0.25em;
      }
    }

    blockquote {
      border-left: 4px solid #ccc;
      padding-left: 1rem;
      margin-left: 0;
      margin-right: 0;
    }

    code {
      background-color: #f0f0f0;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-family: monospace;
    }

    pre {
      background: #f0f0f0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
      margin-bottom: 1rem;

      code {
        background: none;
        padding: 0;
        font-size: 0.85em;
      }
    }

    a {
      color: #3b82f6;
      text-decoration: underline;
    }

    img {
      max-width: 100%;
      height: auto;
      display: block;
      margin-top: 1rem;
      margin-bottom: 1rem;
    }

    // Table styles
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1rem 0;
      table-layout: fixed; // Important for resizable columns

      td, th {
        border: 1px solid #ccc;
        padding: 0.5rem;
        vertical-align: top;
        box-sizing: border-box; // Ensure padding is included in width
        min-width: 50px; // A minimum width for cells
        position: relative; // For resize handles if you implement custom ones
      }

      th {
        background-color: #f3f4f6;
        font-weight: bold;
        text-align: left;
      }

      .selectedCell:after {
        background: #2b7fff;
        color: #333;
        content: "";
        left: 0; right: 0; top: 0; bottom: 0;
        pointer-events: none;
        position: absolute;
        z-index: 2;
        opacity: 0.1;
      }
      .column-resize-handle {
        background-color: #8ec5ff;
        bottom: -2px;
        pointer-events: none;
        position: absolute;
        right: -2px;
        top: 0;
        width: 4px;
      }
    }
    &.resize-cursor {
      cursor: ew-resize;
      cursor: col-resize;
    }
  }

  // Styles for the toolbar buttons (already present, just ensuring they look good)
  button {
    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
}


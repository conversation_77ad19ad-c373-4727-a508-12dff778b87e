import { But<PERSON> } from "@/components/ui/button";
import { Flex } from "@chakra-ui/react";
import { Column } from "@tanstack/react-table";
import { ChevronDown, ChevronUp } from "lucide-react";

interface TableSortButtonsProps<T> {
  column: Column<T, unknown>;
}

const TableSortButtons = <T,>({ column }: TableSortButtonsProps<T>) => {
  return (
    <Flex flexDirection="column" align="center">
      <Button
        className="h-3 w-3 p-0 text-left hover:bg-primary/90 hover:text-white/90"
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        <ChevronUp color="white" size={12} />
      </Button>
      <Button
        className="h-3 w-3 p-0 text-left hover:bg-primary/90 hover:text-white/90"
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        <ChevronDown color="white" size={12} />
      </Button>
    </Flex>
  );
};

export default TableSortButtons;

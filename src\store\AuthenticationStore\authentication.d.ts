export interface AuthenticationState {
  access_token: string | null;
  user?: AuthUser;
  login: (value: ILoginVerifyValue) => Promise<AuthUser>;
  logout: () => void;
}

export interface AuthUser {
  biography: {
    date_of_birth: string;
    employment_status: string;
    first_name: string;
    gender: string;
    last_name: string;
    marital_status: string;
    national_id: string;
    nationality: string;
    other_name: string;
    title: string;
  };
  contact_information: {
    country: string;
    email: string;
    LGA: string;
    next_of_kin: string;
    next_of_kin_phone_number: string;
    phone_number: string;
    residential_address: string;
    state: string;
    city: string;
  };
  created_at: Date;
  display_picture: string;
  email_verified: boolean;
  group: string;
  id: string;
  mfa_verified: boolean;
  role: string;
  updated_at: Date;
}

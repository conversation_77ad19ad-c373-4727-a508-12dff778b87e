import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import Link from "next/link";

export const TableDropdown = ({
  options,
}: {
  options: {
    onClick?: () => void;
    link?: string;
    option: React.ReactNode;
    hasPermission: boolean;
    hide?: boolean;
    disabled?: boolean;
  }[];
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4 rotate-90" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="z-[1400]" align="end">
        {options
          .filter((option) => option.hasPermission && !option.hide)
          .map((option, index) => (
            <DropdownMenuItem
              key={index}
              onClick={option.onClick}
              className="cursor-pointer"
              disabled={option.disabled}
            >
              {option.link ? (
                <Link href={option.link}>{option.option}</Link>
              ) : (
                option.option
              )}
            </DropdownMenuItem>
          ))}
        {options.filter((option) => option.hasPermission && !option.hide)
          .length == 0 && (
          <DropdownMenuItem disabled>No Actions </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

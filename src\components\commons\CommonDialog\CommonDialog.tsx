import React, { <PERSON> } from "react";

import { ICommonDialogProps } from "./CommonDialog.d";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import Icon from "@/components/commons/Icons/Icon";
import { EIconName, EIconSize } from "@/components/commons/Icons/Icon.enums";
import { EColor } from "@/constants/colors";
import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import { ReloadIcon } from "@radix-ui/react-icons";

const CommonDialog: FC<ICommonDialogProps> = ({
  open,
  header,
  children,
  handleCancel,
  handleProceed,
  cancelText,
  confirmText,
  isFooter,
  variant,
  maxWidth,
  confirmLoading,
}) => {
  return (
    <Dialog open={open}>
      <DialogContent
        onOpenAutoFocus={(e) => e.preventDefault()}
        className={`w-full ${maxWidth ? `max-w-${maxWidth}px` : "max-w-[700px]"} p-[16px]`}
        isCloseIcon={false}
      >
        <DialogHeader
          className={`${variant === "danger" ? "!bg-[#E83831]" : "bg-primary"} " text-white" rounded-xl p-2`}
        >
          <DialogTitle className="flex items-center justify-between text-[18px] font-semibold text-white">
            <div>{header}</div>
            <Icon
              onClick={handleCancel}
              size={EIconSize.SM}
              isStaticIcon={true}
              name={EIconName.CLOSE}
              color={EColor.WHITE}
            />
          </DialogTitle>
        </DialogHeader>
        <div className="text-[14px] font-medium text-primary">{children}</div>
        {isFooter && (
          <DialogFooter className="flex items-center sm:justify-center">
            {!!handleCancel && (
              <ButtonCTA
                onClick={handleCancel}
                type="button"
                variant={EButtonType.OUTLINE}
                className="w-full"
              >
                {cancelText || "Cancel"}
              </ButtonCTA>
            )}
            {!!handleProceed && (
              <ButtonCTA
                onClick={handleProceed}
                type="button"
                disabled={confirmLoading}
                className="flex w-full items-center gap-3"
              >
                {confirmLoading && <ReloadIcon className="animate-spin" />}
                {confirmText || "Proceed"}
              </ButtonCTA>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};
export default CommonDialog;

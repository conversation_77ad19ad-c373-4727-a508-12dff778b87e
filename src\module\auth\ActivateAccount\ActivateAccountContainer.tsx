"use client";
import { withFormik } from "formik";

import ActivateAccountForm from "./ActivateAccountForm";

import {
  IActivateAccountContainerProps,
  IActivateAccountValue,
} from "./ActivateAccount";

const ActivateAccountContainer = withFormik<
  IActivateAccountContainerProps,
  IActivateAccountValue
>({
  validate: () => {},
  handleSubmit: () => {},
})(ActivateAccountForm);

export default ActivateAccountContainer;

import { useState, useEffect, ChangeEvent } from "react";
import { ErrorMessage, Field } from "formik";
import { cn } from "@/lib/utils";
import { renderLabel } from "../../../utils/commonsRender";
import { IFieldInputProps } from "./FieldInput.d";
import { E_FIELD_INPUT_TYPE } from "@/constants/enums";
import { formatCurrency } from "@/utils/commons";

const FieldInput = ({
  name,
  label,
  placeholder,
  className,
  isRequire,
  maxLength,
  valueField,
  isDisabled,
  prefixIcon,
  onChange,
  type,
}: IFieldInputProps) => {
  const [displayValue, setDisplayValue] = useState<string>("");

  // Convert currency format back to number
  const parseCurrency = (value: string): number => {
    return Number(value.replace(/[^\d.]/g, ""));
  };

  useEffect(() => {
    if (type === E_FIELD_INPUT_TYPE.CURRENCY && valueField !== undefined) {
      setDisplayValue(formatCurrency(valueField));
    } else {
      setDisplayValue(valueField?.toString() || "");
    }
  }, [valueField, type]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    if (type === E_FIELD_INPUT_TYPE.CURRENCY) {
      // Only allow numbers and one decimal point
      if (/^[\d.]*$/.test(inputValue.replace(/,/g, ""))) {
        const formattedValue = formatCurrency(inputValue);
        setDisplayValue(formattedValue);
        const parsedCurrencyValue = parseCurrency(formattedValue);
        onChange?.(parsedCurrencyValue);
      }
    } else {
      setDisplayValue(inputValue);
      onChange?.(inputValue);
    }
  };

  return (
    <div>
      {label && renderLabel(label, isRequire)}
      <div
        className={cn(
          className,
          "focus:shadow-outline flex h-12 w-full items-center gap-1 rounded border px-3 py-2",
          "leading-tight text-gray-700 shadow-none focus-within:border focus:outline-none focus:ring-0",
        )}
      >
        {prefixIcon && (
          <div className="flex items-center text-[#8EA0AF]">{prefixIcon}</div>
        )}
        {type === "currency" ? (
          <input
            value={displayValue}
            name={name}
            disabled={isDisabled}
            placeholder={placeholder}
            onChange={handleChange}
            className={cn(
              "w-full border-0 bg-transparent focus:border-0 focus:outline-none focus:ring-0",
            )}
          />
        ) : (
          <Field
            value={valueField}
            name={name}
            disabled={isDisabled}
            maxLength={maxLength}
            placeholder={placeholder}
            className={cn(
              "w-full border-0 bg-transparent focus:border-0 focus:outline-none focus:ring-0",
            )}
          />
        )}
      </div>
      <ErrorMessage
        name={name}
        component="div"
        className="mt-1 text-xs text-red-500"
      />
    </div>
  );
};

export default FieldInput;

"use client";
import { BaseColor } from "@/constants/colors";
import { Tab, TabIndicator, Tab<PERSON>ist, TabPanels, Tabs } from "@chakra-ui/react";
import PastExams from "./components/PastExams";
import ExamWelcome from "./components/ExamWelcome";
import UpcomingExams from "./components/UpcomingExams";

const ExamManagement = () => {
  return (
    <div className="mx-auto flex h-full flex-col gap-6 px-2 py-10 sm:px-6 lg:px-[100px]">
      <ExamWelcome />
      <Tabs position="relative" variant="unstyled" className="mt-2">
        <TabList>
          <Tab
            _selected={{
              fontWeight: "semibold",
              fontSize: "16px",
              color: BaseColor.PRIMARY,
            }}
            fontWeight="semibold"
            color={BaseColor.PRIMARY_300}
          >
            Upcoming Exams
          </Tab>
          <Tab
            _selected={{
              fontWeight: "semibold",
              fontSize: "16px",
              color: BaseColor.PRIMARY,
            }}
            fontWeight="semibold"
            color={BaseColor.PRIMARY_300}
          >
            Past Exams
          </Tab>
        </TabList>
        <TabIndicator
          mt="-1.5px"
          height="2px"
          bg={BaseColor.PRIMARY}
          borderRadius="1px"
        />
        <TabPanels>
          <UpcomingExams />
          <PastExams />
        </TabPanels>
      </Tabs>
    </div>
  );
};

export default ExamManagement;

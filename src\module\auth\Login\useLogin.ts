import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { redirect, useRouter } from "next/navigation";
import { useState } from "react";
import { baseApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import {
  ILoginResponse,
  ILoginValue,
  ILoginVerifyResponse,
  ILoginVerifyValue,
} from "./Login";
import { getUserByAccessToken } from "../CAS/cas.utils";

export const useLogin = () => {
  const router = useRouter();
  const toast = useToast();
  const login = useAuthStore((state) => state.login);
  const [verifyEmail, setVerifyEmail] = useState<string>();

  const handleValidateStudentRole = async (access_token: string) => {
    const user = await getUserByAccessToken(access_token);
    if (user.group != "Students") {
      toast({
        title: "Access denied",
        description:
          "You cannot access the student exam portal without a student role",
        status: "error",
      });
      redirect("/login");
      return false;
    }

    return true;
  };

  const handleLogin = async ({ email, password }: ILoginValue) => {
    try {
      const res = await baseApi.post<APIResponse<ILoginResponse>>(
        Routes[MODULE_ROUTE.AUTH].LOGIN,
        { email, password },
        {
          headers: {
            "X-Origin-Portal": "student",
          },
        },
      );
      const access_token = res?.data?.data?.access_token;
      if (access_token) {
        const validStudent = await handleValidateStudentRole(access_token);

        if (!validStudent) return;
        const user = await login(res.data.data);
        toast({
          description: "Login Successful",
          status: "success",
        });

        if (user) {
          router.replace("/exam/choose-center");
        }
      } else {
        setVerifyEmail(email);
        toast({
          description:
            "We have just sent you an email with your verification code",
          status: "success",
        });
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
      console.warn(error);
    }
  };

  const handleLoginVerify = async ({ code }: ILoginVerifyValue) => {
    try {
      const resAuth = await baseApi.post<APIResponse<ILoginVerifyResponse>>(
        Routes[MODULE_ROUTE.AUTH].LOGIN_VERIFY,
        { email: verifyEmail, code },
        {
          headers: {
            "X-Origin-Portal": "student",
          },
        },
      );
      const validStudent = await handleValidateStudentRole(
        resAuth.data.data.access_token,
      );
      const user = await login(resAuth.data.data);
      if (!validStudent) return;
      if (user) {
        router.replace("/exam/choose-center");
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
      console.warn(error);
    }
  };

  return {
    handleLogin,
    handleLoginVerify,
    verifyEmail,
  };
};

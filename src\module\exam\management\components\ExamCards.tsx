import ExamCard from "./ExamCard";
import { BaseColor } from "@/constants/colors";
import { ExamCardsProps } from "../ExamManagement";
import { Grid, GridItem, Text } from "@chakra-ui/react";
import { convertUTCTimeToLocalTimeV2 } from "@/utils/dateUtils";
import InfiniteScroll from "react-infinite-scroll-component";
import CommonLoading from "@/components/commons/CommonLoading/CommonLoading";

interface Infinitescroll extends ExamCardsProps {
  fetch: VoidFunction;
  hasMore: boolean;
  loading: boolean;
}

const ExamCards = ({ data, fetch, hasMore, loading }: Infinitescroll) => {
  return (
    <InfiniteScroll
      dataLength={data.length} //This is important field to render the next data
      next={fetch}
      hasMore={hasMore}
      loader={<></>}
      endMessage={<></>}
    >
      {loading ? (
        <CommonLoading />
      ) : (
        <>
          {data.length === 0 && (
            <Text
              fontWeight="medium"
              fontSize="20px"
              lineHeight="28px"
              textAlign="center"
              color={BaseColor.PRIMARY}
            >
              There are no exams available
            </Text>
          )}
          {data.length > 0 && (
            <Grid
              templateColumns={{
                base: "repeat(1, minmax(0, 1fr))", // Single column on small screens
                sm: "repeat(2, minmax(0, 1fr))",
                xl: "repeat(4, minmax(0, 1fr))",
              }}
              gap={{ base: 4, md: 6 }}
              mx="auto"
              p={{ base: 1, md: 4, lg: 6 }}
            >
              {data.map((item, index) => (
                <GridItem key={index} w="100%" colSpan={1}>
                  <ExamCard
                    hasTaken={item.taken}
                    title={item.title}
                    tags={item.tags}
                    date={convertUTCTimeToLocalTimeV2(
                      item.duration_window_start_date as string,
                    )}
                    time={convertUTCTimeToLocalTimeV2(
                      item.duration_window_start_date as string,
                      true,
                    )}
                    type={"UPCOMING"}
                    id={item.id}
                  />
                </GridItem>
              ))}
            </Grid>
          )}
        </>
      )}
    </InfiniteScroll>
  );
};

export default ExamCards;

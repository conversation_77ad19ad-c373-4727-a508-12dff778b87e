import Image from "next/image";
import ExamLiveTag from "./ExamLiveTag";
import { Card } from "@/components/ui/card";
import { useRouter } from "next/navigation";
import { BaseColor } from "@/constants/colors";
import { Button } from "@/components/ui/button";
import ExamEligibleTag from "./ExamEligibleTag";
import { ExamCardInput } from "../ExamManagement";
import { HStack, VStack, Text } from "@chakra-ui/react";
import ExamIneligibleTag from "./ExamIneligibleTag";
import ExamClosedTag from "./ExamClosedTag";
import { browserUtils } from "@/utils/browserUtils";
import { CheckIcon } from "lucide-react";

const ExamCard = (input: ExamCardInput) => {
  const router = useRouter();
  const handleClick = () => {
    if (browserUtils.isSEBBrowser()) {
      router.push(`/exam/details?exam_id=${input.id}`);
    } else {
      router.push(`/exam/details/${input.id}`);
    }
  };
  return (
    <Card className="flex h-full w-full flex-col justify-between p-3 md:p-[16px]">
      <VStack
        bg={BaseColor.GREY_50}
        p="16px"
        rounded="8px"
        gap="16px"
        mb="16px"
        height="100%"
      >
        <HStack className="flex w-full items-center justify-between">
          <div className="flex gap-2">
            {input.tags.map((tag) => {
              if (tag === "LIVE") {
                return <ExamLiveTag key="LIVE" />;
              } else if (tag === "ELIGIBLE") {
                return <ExamEligibleTag key="ELIGIBLE" />;
              } else if (tag === "INELIGIBLE") {
                return <ExamIneligibleTag key="INELIGIBLE" />;
              } else if (tag === "CLOSED") {
                return <ExamClosedTag key="CLOSED" />;
              }
            })}
          </div>
          {/* <Box className="flex-2" /> */}
          {input.hasTaken && (
            <div className="flex items-center gap-2 text-green-600">
              <CheckIcon /> Submitted
            </div>
          )}
        </HStack>
        <Text
          fontWeight="bold"
          fontSize="20px"
          lineHeight="28px"
          color={BaseColor.PRIMARY}
        >
          {input.title}
        </Text>
        <HStack className="w-full" justifyContent="space-between">
          <HStack gap="10px">
            <Image
              alt="calender"
              src="/images/icons/calender.svg"
              width={20}
              height={20}
            />
            <Text
              fontWeight="medium"
              fontSize="14px"
              color={BaseColor.PRIMARY_300}
            >
              {input.date}
            </Text>
          </HStack>
          <HStack gap="5px">
            <Image
              alt="clock"
              src="/images/icons/clock.svg"
              width={20}
              height={20}
            />
            <Text
              fontWeight="medium"
              fontSize="14px"
              color={BaseColor.PRIMARY_300}
            >
              {input.time}
            </Text>
          </HStack>
        </HStack>
      </VStack>

      {!input.tags.includes("CLOSED") && (
        <Button variant="outline" onClick={handleClick}>
          View Exam
        </Button>
      )}
    </Card>
  );
};

export default ExamCard;

export enum Actions {
  delete = "delete",
  update = "update",
  write = "write",
  read = "read",
}

export enum Modules {
  Dashboards = "Dashboards",
  Applications = "Applications",
  Enrolments = "Enrolments",
  ManageUniversity = "Manage University",
  People = "People",
  Courses = "Courses",
  Programmes = "Programmes",
  Results = "Results",
  Deferment = "Deferment",
  FinancialManagement = "Financial Management",
  Mivas = "mivas",
  Security = "Security",
  ContentManagement = "Content Management",
}

export enum Applications {
  "programme_applications" = "programme_applications",
}

export enum Dashboards {
  "application_analytics" = "application_analytics",
  "enrollment_analytics" = "enrollment_analytics",
  "finance_analytics" = "finance_analytics",
}

export enum Enrolments {
  "manage_enrolment" = "manage_enrolment",
  "programme_enrolments" = "programme_enrolments",
}

export enum ManageUniversity {
  "atp" = "academic_time_period",
  "departments" = "departments",
  "faculties" = "faculties",
}

export enum People {
  "users" = "users",
  "student" = "student",
  "staff" = "staff",
}

export enum Courses {
  "manage_courses" = "manage_courses",
  "course_offerings" = "course_offerings",
}

export enum Programmes {
  "manage_programmes" = "manage_programmes",
  "programme_bundles" = "programme_bundles",
  "programme_intake" = "programme_intake",
}

export enum Results {
  "course_results" = "course_results",
  "programme_results" = "programme_results",
  "grade_scale" = "grade_scale",
}

export enum Deferment {
  "manage_deferment" = "manage_deferment",
  "deferment_requests" = "deferment_requests",
}

export enum Mivas {
  "email_template_data" = "email_template_data",
}

export enum Security {
  "roles" = "roles",
}

export enum FinancialManagement {
  "receipts" = "receipts",
  "manage_student_accounts" = "manage_student_accounts",
  "programme_intake_products" = "programme_intake_products",
  "vouchers" = "vouchers",
}

export enum ContentManagement {
  "email_templates" = "email_templates",
  "document_templates" = "document_templates",
}

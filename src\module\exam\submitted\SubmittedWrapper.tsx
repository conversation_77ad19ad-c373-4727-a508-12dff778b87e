"use client";

import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import { Image, Text, VStack } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import React from "react";

const ExamSubmittedWrapper = () => {
  const router = useRouter();
  return (
    <VStack
      width="100%"
      height="100%"
      alignItems="center"
      justifyContent="center"
    >
      <Image alt="check-circle" src="/images/icons/check-circle.svg" />
      <Text fontWeight="bold" fontSize="24px">
        Exam Submitted
      </Text>
      <Text fontWeight="medium" fontSize="16px">
        Your exam has been successfully submitted.
      </Text>
      <ButtonCTA
        onClick={() => router.push("/exam/management")}
        type="button"
        variant={EButtonType.PRIMARY}
        className="mx-auto w-full max-w-[169px] text-sm font-semibold text-white"
      >
        Go to Dashboard &gt;
      </ButtonCTA>
    </VStack>
  );
};

export default ExamSubmittedWrapper;

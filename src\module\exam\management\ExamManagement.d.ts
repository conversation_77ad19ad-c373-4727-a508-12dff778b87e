import { Paginated } from "@/api/config/api";

type ExamCardInput = {
  title: string;
  tags: ("LIVE" | "ELIGIBLE" | "INELIGIBLE" | "CLOSED")[];
  date: string;
  time: string;
  type: "UPCOMING" | "PAST";
  hasTaken: boolean;
  id: string;
};

export interface ExamCard {
  allow_random_question_selections: boolean;
  course: {
    id: string;
    name: string;
  };
  created_at: Date | string;
  date: Date | string;
  description: string;
  duration: number;
  duration_window_end_date: Date | string;
  duration_window_start_date: Date | string;
  faculty: {
    id: string;
    name: string;
  };
  tags: ("LIVE" | "ELIGIBLE" | "INELIGIBLE" | "CLOSED")[];
  id: string;
  instructions: string;
  no_questions: 5;
  programme_id: string;
  programme_intake_id: string;
  publish_at: Date | string;
  question_order: string;
  sitting: "ONLINE_AND_IN_PERSON" | "ONLINE_ONLY";
  status: string;
  timing: string;
  title: string;
  updated_at: Date | string;
  is_eligible?: boolean;
  course_id: string;
  taken: boolean;
  in_progress: boolean;
  time_has_expired?: boolean;
}

export type ExamCardsProps = {
  data: ExamCard[];
};

export type PaginatedExam<T> = Paginated<T> & {
  loading: boolean;
};

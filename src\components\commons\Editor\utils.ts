const convertLatexInDoc = (editor: any) => {
  if (!editor) return;
  const regex = /\$\$([\s\S]+?)\$\$|\$([^$]+?)\$/g;
  const { view } = editor;
  const state = editor.state;
  let tr = state.tr;
  // Collect all matches first
  const matches: any[] = [];
  state.doc.descendants((node: any, pos: any) => {
    if (node.isText) {
      let m;
      while ((m = regex.exec(node.text)) !== null) {
        matches.push({
          isBlock: !!m[1],
          content: m[1] || m[2],
          from: pos + m.index,
          to: pos + m.index + m[0].length,
          raw: m[0],
        });
      }
    }
  });
  // Replace from the end to avoid messing up positions
  for (let i = matches.length - 1; i >= 0; i--) {
    const { isBlock, content, from, to } = matches[i];
    const mathNode = state.schema.nodes["inlineMath"]?.create({
      latex: content,
      displayMode: isBlock ? "block" : "inline",
    });
    tr = tr.replaceWith(from, to, mathNode || state.schema.text(content));
  }
  if (tr.docChanged) {
    view.dispatch(tr);
  }
};

export { convertLatexInDoc };

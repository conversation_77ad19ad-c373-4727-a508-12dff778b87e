import React from "react";
import { Text } from "@chakra-ui/react";

const IneligibilityInstructions = () => {
  return (
    <>
      <Text
        fontSize="24px"
        fontWeight="bold"
        textAlign="center"
        className="mx-auto my-4"
      >
        Instructions
      </Text>
      <div className="mx-auto max-w-[1024px] bg-[#FDEBEA] p-6">
        <Text fontSize="16px" fontWeight="bold" textAlign="left">
          You Are Not Eligible for This Exam
        </Text>

        <Text fontSize="16px" fontWeight="medium" textAlign="left">
          We have determined that you do not meet the requirements to take this
          exam at this time. This may be due to:
        </Text>
        <Text
          fontSize="16px"
          fontWeight="medium"
          textAlign="left"
          className="my-4"
        >
          <span className="text-base font-bold">• Unpaid School Fees: </span>
          <span className="">
            You may have outstanding balances or fees that need to be cleared
            before you can proceed. Please check your student account or billing
            statements to ensure all tuition, library fines, and any other fees
            have been paid in full.
          </span>
        </Text>
        <Text
          fontSize="16px"
          fontWeight="medium"
          textAlign="left"
          className="my-4"
        >
          <span className="text-base font-bold">
            • Incomplete Prerequisites:{" "}
          </span>
          <span className="">
            Certain courses or exam components are required to be finished
            beforehand. Verify that you have successfully completed all previous
            coursework, exams, or certifications necessary for this exam. If
            you&apos;re unsure, consult your academic progress report or speak
            with an advisor.
          </span>
        </Text>
        <Text
          fontSize="16px"
          fontWeight="medium"
          textAlign="left"
          className="my-4"
        >
          <span className="text-base font-bold">• Administrative Holds:</span>
          <span className="">
            There may be an official hold on your student account. Holds can be
            placed for reasons such as missing documentation, required updates
            to your student information, or unresolved academic or disciplinary
            issues. Check your student portal or contact the administration to
            determine the specific nature of any hold.
          </span>
        </Text>
        <span className="my-4">
          For more information, please reach out to your Success Advisor or
          contact us at{" "}
          <b>
            <a href="mailto:<EMAIL>."><EMAIL>.</a>
          </b>
        </span>
      </div>
    </>
  );
};

export default IneligibilityInstructions;

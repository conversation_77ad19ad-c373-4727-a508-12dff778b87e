export const undefinedToString = (s?: string, defaultString = ""): string => {
  return s || defaultString;
};

export const beautify = (string?: string): string => {
  return undefinedToString(string).replaceAll("_", " ").trim();
};

export const capitalizeFirstLetter = (val: string): string => {
  return String(val).charAt(0).toUpperCase() + String(val).slice(1);
};

export const capitalizeAllFirstLetters = (val: string): string =>
  val.replace(
    /(^\w|\s\w)(\S*)/g,
    (_, m1, m2) => m1.toUpperCase() + m2.toLowerCase(),
  );

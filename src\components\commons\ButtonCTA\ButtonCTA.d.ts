import { ButtonProps } from "@chakra-ui/react";
import { EButtonType } from "@/components/commons/ButtonCTA/ButtonCTA";
import { ReactNode } from "react";
import { ButtonProps as OriginalButtonProps } from "@/components/ui/button";

// export interface IButtonCTAProps extends ButtonProps {
//   variant?: EButtonType;
//   children: ReactNode;
//   title?: string;
//   isDisabled?: boolean;
// }

interface IButtonCTAProps
  extends Omit<OriginalButtonProps, "color">,
    Omit<ButtonProps, "color"> {
  variant?: EButtonType;
  children?: ReactNode;
  title?: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  loadingText?: string;
}

import { IOptionsItem } from "@/constants/types";

export const PAGING = {
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_PAGE_SIZE_20: 20,
  DEFAULT_PAGE_SIZE_50: 50,
  DEFAULT_PAGE_SIZE_100: 100,
  DEFAULT_CURRENT_PAGE: 1,
};

export const TIME = {
  DEFAULT_DEBOUNCE_INPUT: 300,
  DEBOUNCE_COPY_TO_CLIPBOARD: 2000,
};

export const OPTION_PAGE_SIZE: IOptionsItem[] = [
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE),
    value: PAGING.DEFAULT_PAGE_SIZE,
  },
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE_20),
    value: PAGING.DEFAULT_PAGE_SIZE_20,
  },
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE_50),
    value: PAGING.DEFAULT_PAGE_SIZE_50,
  },
  {
    label: String(PAGING.DEFAULT_PAGE_SIZE_100),
    value: PAGING.DEFAULT_PAGE_SIZE_100,
  },
];

export const CURRENCY_LIST = {
  NAIRA: "₦",
  USD: "$",
};

export const CURRENCY_CODE = {
  NGN: "NGN",
  USD: "USD",
};

export const CURRENCY_OPTIONS = [
  {
    label: "NAIRA",
    value: CURRENCY_CODE.NGN,
    symbol: CURRENCY_LIST.NAIRA,
  },
  {
    label: "USD",
    value: CURRENCY_CODE.USD,
    symbol: CURRENCY_LIST.USD,
  },
];

export const CURRENCY_CODE_OPTIONS = [
  {
    label: "NGN",
    value: CURRENCY_CODE.NGN,
  },
  {
    label: "USD",
    value: CURRENCY_CODE.USD,
  },
];

export const MAP_CURRENCY_CODE_SYMBOLS = {
  [CURRENCY_CODE.NGN]: CURRENCY_LIST.NAIRA,
  [CURRENCY_CODE.USD]: CURRENCY_LIST.USD,
};

export const QUESTION_TYPES = {
  ESSAY: "ESSAY",
  MULTIPLE_CHOICE: "MULTIPLE_CHOICE",
  MULTIPLE_FILL_IN_THE_BLANKS: "MULTIPLE_FILL_IN_THE_BLANKS",
};

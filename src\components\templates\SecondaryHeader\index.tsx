"use client";

import React from "react";
import { Image } from "@chakra-ui/react";
import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import { useRouter } from "next/navigation";

const SecondaryHeader = () => {
  const router = useRouter();

  const viewProgrammes = () => {};

  return (
    <div className="flex h-[62px] w-full items-center justify-between bg-white px-10 py-3 lg:px-28">
      <Image
        alt="Logo"
        src="/images/logo.svg"
        onClick={() => router.push("/")}
      />
      <ButtonCTA
        className="bg-[#E83831] text-[12px] font-bold text-white"
        onClick={() => viewProgrammes()}
        variant={EButtonType.SECONDARY}
      >
        View Programmes
      </ButtonCTA>
    </div>
  );
};

export default SecondaryHeader;

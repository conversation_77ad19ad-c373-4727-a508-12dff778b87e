/* eslint-disable @typescript-eslint/no-explicit-any */
import { EVIEW_PORT } from "./enums";

export type TParamPageCommon = {
  params?: {
    [key: string]: string | string[] | undefined;
  };
  searchParams: {
    viewport: EVIEW_PORT;
  };
};

export interface ObjectString {
  [key: string]: string;
}

export interface AnyObject {
  [key: string]: any;
}

export interface IOptionsItem {
  label: string;
  value: string | number;
}
export type ExamCardInput = {
  title: string;
  tags: ("LIVE" | "ELIGIBLE" | "INELIGIBLE" | "CLOSED")[];
  date: string;
  time: string;
  type: "UPCOMING" | "PAST";
};

/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from "react";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { ICommonTabsProps, E_TAB_TYPE } from "./CommonTabs.d";

import "@/styles/customTabs.scss";

const CommonTabs: FC<ICommonTabsProps> = ({
  tabs,
  defaultTabKey,
  onChange,
  className,
  activeTabKey,
  isSticky,
  classNameTabList,
  type = E_TAB_TYPE.DEFAULT,
}) => {
  return (
    <Tabs
      defaultValue={defaultTabKey}
      onValueChange={(value) => onChange(value)}
      className={cn("custom-tabs w-full", className, {
        "tab-common-default": type === E_TAB_TYPE.DEFAULT,
        "tab-card-common": type === E_TAB_TYPE.TAB_CARD,
      })}
    >
      <TabsList
        className={cn("tab-items", classNameTabList, {
          "sticky top-[60px] z-10": isSticky,
          "tab-list": type === E_TAB_TYPE.TAB_CARD,
        })}
      >
        {tabs.map((item) => {
          return (
            <TabsTrigger
              className={cn("tab-name-item h-full", {
                active: activeTabKey === item.tabKey,
              })}
              key={`tab-name-${item.tabKey}`}
              value={item.tabKey}
            >
              {item.tabName}
            </TabsTrigger>
          );
        })}
      </TabsList>
      {tabs.map((item) => {
        return (
          <TabsContent
            className="tab-content"
            key={`tab-content-${item.tabKey}`}
            value={item.tabKey}
          >
            <div className="min-h-[70vh] bg-white pb-[20px]">
              {item.component}
            </div>
          </TabsContent>
        );
      })}
    </Tabs>
  );
};

export default CommonTabs;

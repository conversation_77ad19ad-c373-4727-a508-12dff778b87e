export interface GetSingleQuestionRequestType {
  exam_id: string;
  session_id: string;
  question_id: string;
}
export interface GetSingleQuestionResponseType {
  answer_text: string;
  created_at: string;
  id: string;
  options: Option[];
  points: number;
  question: string;
  question_type: string;
  selected_option_id: string;
  updated_at: string;
  word_count: number;
}

export interface Option {
  id: string;
  value: string;
}

import { HStack, Text } from "@chakra-ui/react";
import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { BaseColor } from "@/constants/colors";
import { ExamTitleProps } from "../ExamDetails";

const ExamTitle = ({ title }: ExamTitleProps) => {
  const router = useRouter();
  const onClickRouteBack = () => {
    router.push("/exam/management");
  };
  return (
    <HStack
      cursor="pointer"
      mb="50px"
      px="100px"
      className="!gap-x-9 bg-white py-6"
    >
      <Image
        src="/images/icons/arrow-back.svg"
        onClick={onClickRouteBack}
        alt="back button"
        className="h-6 w-6"
        width={24}
        height={24}
      />
      <Text fontWeight="bold" fontSize="32px" color={BaseColor.PRIMARY}>
        {title}
      </Text>
    </HStack>
  );
};

export default ExamTitle;

/* eslint-disable react-hooks/exhaustive-deps */
import React, { FC, useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  SelectGroup,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
// import { ScrollArea } from "@/components/ui/scroll-area";

import { ICommonSelectWithSearchProps } from "./CommonSelectWithSearch.d";

const CommonSelectWithSearch: FC<ICommonSelectWithSearchProps> = ({
  value,
  label,
  options,
  onChange,
  showIndicatorIcon = true,
  className,
  labelContent,
  autoSelectFirstOption = true,
  placeholder = "Search...",
  isDisabled,
  defaultOption,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredOptions, setFilteredOptions] = useState(options);

  useEffect(() => {
    // Auto-select the first option if no value is provided
    if (autoSelectFirstOption && !value && options.length > 0) {
      onChange(String(options[0].value));
    }
  }, [value, options, onChange]);

  useEffect(() => {
    const filtered = options.filter((option) =>
      option?.label?.toLowerCase().includes(searchTerm.toLowerCase()),
    );
    setFilteredOptions(filtered);
  }, [searchTerm, options]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  return (
    <Select onValueChange={onChange} value={String(value)}>
      <SelectTrigger
        className={className}
        showIndicatorIcon={showIndicatorIcon}
        disabled={isDisabled}
      >
        <div className="flex items-center justify-start gap-[8px]">
          {!!label && <span>{label}</span>}
          <SelectValue placeholder={defaultOption || "Select an option"} />
        </div>
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {!!labelContent && <SelectLabel>{labelContent}</SelectLabel>}
          <Input
            placeholder={placeholder}
            value={searchTerm}
            onChange={handleSearch}
            className="mb-2 focus:!ring-0"
          />
          {options.length === 0 && (
            <div className="p-[16px] text-[14px] font-[600] text-[#5B758A]">
              There is no data!
            </div>
          )}
          <div className="h-auto overflow-hidden overflow-y-scroll">
            {filteredOptions.map((option, index) => (
              <SelectItem key={index} value={String(option.value)}>
                {option.label}
              </SelectItem>
            ))}
          </div>
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default CommonSelectWithSearch;

import { type Page, type Locator } from "@playwright/test";

class DashboardPage {
  readonly profileAvatarButton: Locator;
  readonly logoutMenuItem: Locator;

  constructor(page: Page) {
    this.profileAvatarButton = page.getByRole("button", {
      name: "Avatar Disposable",
    });
    this.logoutMenuItem = page.getByRole("menuitem", { name: "Logout" });
  }

  async logout() {
    await this.profileAvatarButton.click();
    await this.logoutMenuItem.click();
  }
}

export default DashboardPage;

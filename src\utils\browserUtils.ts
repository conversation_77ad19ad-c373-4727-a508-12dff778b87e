export const browserUtils = {
  /**
   * Check if the current browser is <PERSON> Ex<PERSON>er (SEB)
   * SEB includes 'SEB' in its user agent string
   */
  isSEBBrowser: (): boolean => {
    if (typeof window === "undefined") {
      return false; // Handle SSR case
    }

    // Check user agent for SEB
    const userAgent = window.navigator.userAgent.toLowerCase();
    const isSEB = userAgent.includes("seb");

    // Additional SEB-specific check
    const hasSEBUserAgent =
      typeof window.navigator.userAgent === "string" &&
      window.navigator.userAgent.indexOf("SEB") !== -1;

    // Check for SEB-specific properties
    const hasSEBConfig = !!(window as any).SEB_Config;

    return isSEB || hasSEBUserAgent || hasSEBConfig;
  },

  /**
   * Get browser information
   */
  getBrowserInfo: () => {
    if (typeof window === "undefined") {
      return {
        userAgent: "",
        isSEB: false,
        browserName: "unknown",
      };
    }

    const userAgent = window.navigator.userAgent;
    const isSEB = browserUtils.isSEBBrowser();

    // Determine browser name
    let browserName = "unknown";
    if (userAgent.indexOf("Chrome") > -1) {
      browserName = "Chrome";
    } else if (userAgent.indexOf("Firefox") > -1) {
      browserName = "Firefox";
    } else if (userAgent.indexOf("Safari") > -1) {
      browserName = "Safari";
    } else if (userAgent.indexOf("Edge") > -1) {
      browserName = "Edge";
    }

    if (isSEB) {
      browserName = "Safe Exam Browser";
    }

    return {
      userAgent,
      isSEB,
      browserName,
    };
  },

  /**
   * Check if the browser is supported for exam taking
   */
  isExamCompatibleBrowser: (): boolean => {
    return (
      browserUtils.isSEBBrowser() || process.env.NODE_ENV === "development"
    );
  },
};

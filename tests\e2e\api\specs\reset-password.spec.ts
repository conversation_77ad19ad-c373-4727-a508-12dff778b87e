import { test, expect } from "@playwright/test";
import { apiLogin, resetPassword } from "../../utils/api-utils";
import { generateRandomPassword } from "../../utils/utils";

const email = "<EMAIL>";
const password = generateRandomPassword(12);
const confirmPassword = password;

test("reset password", async ({ page }) => {
  let responseBody = await resetPassword(email, password, confirmPassword);
  expect(responseBody.message).toEqual("password was reset successfully");
  expect(responseBody.data).toEqual("<EMAIL>");

  // Login with the new password to be sure that it truely changed.
  responseBody = await apiLogin(email, password);
  expect(responseBody?.data).toHaveProperty("access_token");
});

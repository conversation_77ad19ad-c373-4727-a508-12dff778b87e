/* eslint-disable @typescript-eslint/no-explicit-any */
import { AnyObject } from "@/constants/types";
import { format, isValid } from "date-fns";

export const getFullName = (firstName = "", lastName = ""): string => {
  return [firstName, lastName].filter(Boolean).join(" ").trim();
};

export const shallowEqual = (obj1: AnyObject, obj2: AnyObject) => {
  // Check if both objects are the same reference
  if (obj1 === obj2) return true;

  // Check if both are objects
  if (
    typeof obj1 !== "object" ||
    typeof obj2 !== "object" ||
    obj1 === null ||
    obj2 === null
  ) {
    return false;
  }

  // Check the number of properties
  const keys1: string[] = Object.keys(obj1);
  const keys2: string[] = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  // Compare each property
  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
};

export const displayDateByFormat = (date: string, formatType: string) => {
  return isValid(new Date(date)) ? format(new Date(date), formatType) : "";
};

export const splitCountryCode = (phoneNumber: string, prefix: string) => {
  if (!phoneNumber || !prefix) {
    return {
      countryCode: "",
      phoneNumber: phoneNumber || "",
    };
  }

  if (phoneNumber.startsWith(prefix)) {
    return {
      countryCode: prefix,
      phoneNumber: phoneNumber.slice(prefix.length).trim(),
    };
  }

  return {
    countryCode: "",
    phoneNumber: phoneNumber.trim(),
  };
};

export const omitEmptyValues = (obj: Record<string, any>) => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      //@typescript-eslint/no-unused-vars
      ([_, value]) => value !== null && value !== undefined && value !== "",
    ),
  );
};

export const formatCurrency = (value: string | number): string => {
  // Remove all non-digit characters except decimal point
  const cleanValue = value.toString().replace(/[^\d.]/g, "");

  // Ensure only one decimal point
  const parts = cleanValue.split(".");
  if (parts.length > 2) {
    parts.splice(2);
  }

  // Format the integer part with commas
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Handle decimal part
  const decimalPart = parts[1] ? parts[1].slice(0, 2) : "";

  return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
};

const currentYear = new Date().getFullYear();
export const dateRangeOption = Array.from({ length: 4 }, (_, i) => {
  const year = currentYear - i;
  return {
    label: year.toString(),
    value: year.toString(),
  };
});

export const mockStateOptions = [
  "Abia",
  "Adamawa",
  "Akwa Ibom",
  "Anambra",
  "Bauchi",
  "Bayelsa",
  "Benue",
  "Borno",
  "Cross River",
  "Delta",
  "Ebonyi",
  "Edo",
  "Ekiti",
  "Enugu",
  "Gombe",
  "Imo",
  "Jigawa",
  "Kaduna",
  "Kano",
  "Katsina",
  "Kebbi",
  "Kogi",
  "Kwara",
  "Lagos",
  "Nasarawa",
  "Niger",
  "Ogun",
  "Ondo",
  "Osun",
  "Oyo",
  "Plateau",
  "Rivers",
  "Sokoto",
  "Taraba",
  "Yobe",
  "Zamfara",
].map((item) => ({ label: item, value: item }));

export const mockExamCentres = [
  "Ikeja, Lagos",
  "Ikorodu, Lagos",
  "Abeokuta, Ogun",
  "Oshogbo, Ogun",
  "Ibadan, Oyo",
  "Aba, Oyo",
  "Owerri, Imo",
  "Lekki, Lagos",
  "Ibadan, Oyo",
].map((item) => ({ label: item, value: item }));

export const mockExamCenterChangeReasons = [
  "Relocation or Travel",
  "Proximity",
  "Security or Safety Concerns",
  "Errors During Registration",
  "Others",
].map((item) => ({ label: item, value: item }));

export const extractErrorFromApi = <T>(arr: T[]): T | undefined => {
  return arr.length > 0 ? arr[0] : undefined;
};

export function isExamLive(
  startDate: string,
  endDate: string,
  eligibility: boolean,
): ("INELIGIBLE" | "ELIGIBLE" | "LIVE" | "CLOSED")[] {
  const now = new Date();
  const start = new Date(startDate);
  const end = new Date(endDate);
  if (eligibility) {
    if (now >= start && now <= end) {
      return ["LIVE", "ELIGIBLE"];
    }
    return ["ELIGIBLE"];
  }

  return ["INELIGIBLE"];
}

export const examInfo = [
  {
    title: "Examination",
    subtitle: "Date",
    value: "12 December",
    icon: "/images/icons/calendar2.svg",
  },
  {
    title: "Examination",
    subtitle: "Time",
    value: "09:30 AM",
    icon: "/images/icons/exam-details-clock.svg",
  },
  {
    title: "Number of",
    subtitle: "Questions",
    value: "130",
    icon: "/images/icons/chat-info.svg",
  },
  {
    title: "Number of",
    subtitle: "Questions",
    value: "60",
    icon: "/images/icons/chat-info.svg",
  },
];

export const instructions = [
  "Make sure your device is connected to a reliable network. Disconnection may disrupt your exam.",
  "You can move between questions freely by using the 'Next' and 'Previous' buttons. Be sure to review your answers before submission.",
  "The exam will be auto-submitted when the time limit is reached. Please manage your time accordingly.",
  "You need to Download the SEB before proceeding to the exam.",
  "This exam is being monitored. Any attempts to cheat or violate exam rules will result in disqualification or disciplinary actions.",
  "Once you click 'Start Exam', the timer will begin, and you&apos;ll need to complete the exam in one sitting.",
];

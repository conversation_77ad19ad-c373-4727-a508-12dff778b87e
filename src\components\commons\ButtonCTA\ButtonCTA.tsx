import { FC } from "react";
// import { But<PERSON> } from "@chakra-ui/react";
import { ReloadIcon } from "@radix-ui/react-icons";
import { Button } from "@/components/ui/button";
import { IButtonCTAProps } from "./ButtonCTA.d";
import { cn } from "@/lib/utils";

export enum EButtonType {
  DESTRUCTIVE = "destructive",
  PRIMARY = "primary",
  DEFAULT = "default",
  SECONDARY = "secondary",
  OUTLINE = "outline",
  GHOST = "ghost",
  LINK = "link",
}

const ButtonCTA: FC<IButtonCTAProps> = ({
  className,
  children,
  isLoading,
  variant = EButtonType.DEFAULT,
  isDisabled,
  ...rest
}) => {
  return (
    <Button
      className={cn("focus:!ring-0", className)}
      variant={variant}
      disabled={isLoading || isDisabled}
      {...rest}
    >
      {isLoading && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
      {children}
    </Button>
  );
};

export default ButtonCTA;

"use client";

import { But<PERSON> } from "@/components/ui/button";
import { BaseColor } from "@/constants/colors";
import { Box, HStack, Image } from "@chakra-ui/react";
import React from "react";
import { ReloadIcon } from "@radix-ui/react-icons";

type NextAndPreviousProps = {
  page: number;
  max: number;
  onNext: () => void;
  onPrevious: () => void;
  loading: boolean;
};
export const NextAndPrevious = ({
  page,
  max,
  onNext,
  onPrevious,
  loading,
}: NextAndPreviousProps) => {
  return (
    <HStack justifyContent="space-between" width="100%" mb="26px">
      <Button
        variant="ghost"
        className="w-[125px] border border-[#BDC2C9] text-[14px]"
        color={BaseColor.PRIMARY}
        onClick={onPrevious}
        disabled={page <= 1}
      >
        <Image src="/images/icons/navigation-back.svg" mr="4px" alt="back" />
        Previous
      </Button>

      <Button
        variant="ghost"
        className="w-[125px] border border-[#BDC2C9] text-[14px]"
        color={BaseColor.PRIMARY}
        onClick={onNext}
        disabled={page >= max}
      >
        {loading && <ReloadIcon className="animate-spin" />}
        <span className="flex items-center justify-center gap-3">
          Next
          <Image
            src="/images/icons/navigation-forward.svg"
            ml="4px"
            alt="forward"
          />
        </span>
      </Button>
    </HStack>
  );
};

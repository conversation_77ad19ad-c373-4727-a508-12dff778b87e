import { test } from "@playwright/test";
import LoginPage from "../pages/login-page";
import uiPages from "../../utils/ui-pages";

// const email = process.env.MIVA_ADMIN_EMAIL!;
// const password = process.env.MIVA_ADMIN_PASSWORD!;

const email = "<EMAIL>";
const password = "Qa1@miva";
let loginPage: LoginPage;

test.use({ storageState: { cookies: [], origins: [] } });

test("Admin can login", { tag: ["@smoke", "@positive"] }, async ({ page }) => {
  loginPage = new LoginPage(page);
  await page.goto(uiPages.login);
  await loginPage.doLogin(email, password);
  await loginPage.checkLoggedIn();
});

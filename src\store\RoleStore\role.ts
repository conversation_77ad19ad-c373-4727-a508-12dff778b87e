import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { RoleState } from "./role.d";

export const useRoleStore = create<RoleState>()(
  devtools(
    persist(
      //@typescript-eslint/no-unused-vars
      (set) => ({
        permissions: [],
        assigned_role: null,
      }),
      {
        name: "role-storage",
        getStorage: () => localStorage,
        merge: (persistedState, currentState) => ({
          ...currentState,
          ...(persistedState as RoleState),
        }),
      },
    ),
    {
      name: "roleStore",
      enabled: true,
    },
  ),
);

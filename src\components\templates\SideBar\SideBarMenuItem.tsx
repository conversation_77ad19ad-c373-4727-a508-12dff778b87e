"use client";
import React, { ReactElement, useEffect, useMemo } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

import SidebarIcon from "./SideBarIcon";
import { SideBarMenuItemLinkProps, SideBarMenuItemProps } from "./SideBar.d";
import { cn } from "@/lib/utils";
import { useSideBarStore } from "../../../store/SideBarStore/sideBar";

const SideBarMenuItemLink: React.FC<SideBarMenuItemLinkProps> = ({
  parentId,
  menuId,
  to,
  title,
  onToggle,
  iconName,
}) => {
  const pathname = usePathname();
  const isActive = useMemo(() => {
    return !!to && pathname?.includes(to || "");
  }, [to, pathname]);

  const childrenProps = {
    iconName,
    isActive,
    to,
    title,
  };

  useEffect(() => {
    if (isActive && parentId) {
      document.getElementById(parentId)?.classList.add("show");
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  return to ? (
    <Link
      className={cn("menu-link cursor-pointer", {
        active: isActive,
      })}
      href={to}
      onClick={() => onToggle(parentId, menuId)}
    >
      <div className="menu-title-content">
        <SidebarIcon {...childrenProps} />
      </div>
    </Link>
  ) : (
    <div
      className={cn("menu-link cursor-pointer", {
        active: isActive,
      })}
      onClick={() => onToggle(parentId, menuId)}
    >
      <div className="menu-title-content">
        <SidebarIcon {...childrenProps} />
      </div>
    </div>
  );
};

const SideBarMenuItem: React.FC<SideBarMenuItemProps> = ({
  title,
  to,
  iconName,
  parent,
  children,
  index,
  parentId,
  subMenus,
}) => {
  const isExpand = useSideBarStore((state) => state.isExpandSideBar);
  const pathname = usePathname();
  const isParent = subMenus && subMenus?.length > 0;
  const isActive = useMemo(() => {
    return !!to && pathname?.includes(to || "");
  }, [to, pathname]);
  const toggleNode = (toggleNodeParentId: string, menuId: string) => {
    const toggleId = toggleNodeParentId || menuId;
    if (!toggleId) return;
    console.log("toggleId -->", toggleId);
    const currentNode = document
      .getElementById(toggleId)
      ?.classList.contains("show");
    const showedMenus = document.getElementsByClassName("show");
    for (let i = 0; i < showedMenus.length; i++) {
      showedMenus[i].classList.remove("show");
    }
    !currentNode && document.getElementById(toggleId)?.classList.toggle("show");
  };

  return (
    <>
      {children !== 0 && (
        <div
          className={cn("menu-item menu-accordion", {
            "flex justify-center": !isExpand,
            "is-active": isActive,
          })}
          id={`menu-item-${index}-${title.toLowerCase()}`}
        >
          <SideBarMenuItemLink
            to={to}
            title={title}
            parent={parent}
            menuId={`menu-item-${index}-${title.toLowerCase()}`}
            parentId={parentId}
            onToggle={(toggleNodeParentId: string, menuId: string) =>
              !parentId && toggleNode(toggleNodeParentId, menuId)
            }
            iconName={iconName}
            subMenus={subMenus}
          />
          {children && (
            <div className="menu-sub menu-sub-accordion menu-active-bg">
              {React.Children.map(
                children,
                (child) =>
                  child &&
                  React.cloneElement(child as ReactElement, {
                    subNode: true,
                    parent: { title, to },
                    isParent,
                    parentId: `menu-item-${index}-${title.toLowerCase()}`,
                  }),
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
};

export { SideBarMenuItem };

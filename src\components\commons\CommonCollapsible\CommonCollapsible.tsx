import { FC, useState } from "react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

import { ICommonCollapsibleProps } from "./CommonCollapsible.d";
import { EIconName } from "@/components/commons/Icons/Icon.enums";
import Icon from "@/components/commons/Icons/Icon";
import { EColor } from "@/constants/colors";

const CommonCollapsible: FC<ICommonCollapsibleProps> = ({
  customsTitle,
  children,
  className,
  classNameTrigger,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={cn(className)}
    >
      <div className="flex w-full items-center">
        {customsTitle}
        <CollapsibleTrigger
          className={cn(classNameTrigger, "ml-auto cursor-pointer self-start")}
        >
          <span
            className={cn("transition-transform duration-500", {
              "rotate-180": isOpen,
            })}
            role="button"
            aria-label="Toggle collapsible"
          >
            <Icon name={EIconName.ANGLE_UP} color={EColor.TARAWERA} />
          </span>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent
        className={cn(
          "overflow-hidden transition-all duration-300 ease-in-out",
          {
            "max-h-0": !isOpen, // Collapsed state
            "max-h-fit": isOpen, // Expanded state, adjust the height based on content
          },
        )}
      >
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default CommonCollapsible;

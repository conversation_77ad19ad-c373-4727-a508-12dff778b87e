import "@/styles/globals.css";

import { Metadata } from "next";
import { Manrope } from "next/font/google";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ReactNode } from "react";

import { MainProvider } from "@/components/providers/MainProvider";
import { MainLayout } from "@/components/templates/MainLayout";
import { ChakraProvider } from "@chakra-ui/react";

import { cn } from "@/lib/utils";
import * as dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween";
import ProctoringProvider from "src/providers/proctor/ProctoringProvider";

const manrope = Manrope({ subsets: ["latin"], variable: "--font-primary" });
const cinzel = Cinzel({
  subsets: ["latin"],
  variable: "--font-secondary",
  weight: ["400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "Miva Student Exam Portal",
  description:
    "Miva Student Exam Portal designed for educational institutions to manage exams.",
};

interface RootLayoutProps {
  children: ReactNode;
}

dayjs.extend(isBetween);

const RootLayout = ({ children }: RootLayoutProps) => {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://unicons.iconscout.com/release/v4.0.8/css/line.css"
        />
        <link
          rel="stylesheet"
          href="https://unicons.iconscout.com/release/v4.0.8/css/solid.css"
        />
        <link
          rel="stylesheet"
          href="https://unicons.iconscout.com/release/v4.0.8/css/thinline.css"
        />
      </head>
      <body
        className={cn(
          manrope.variable,
          cinzel.variable,
          "overflow-x-hidden font-primary",
        )}
        suppressHydrationWarning
      >
        <ChakraProvider>
          <MainProvider>
            <ProctoringProvider>
              <MainLayout>{children}</MainLayout>
            </ProctoringProvider>
          </MainProvider>
        </ChakraProvider>
      </body>
    </html>
  );
};

export default RootLayout;

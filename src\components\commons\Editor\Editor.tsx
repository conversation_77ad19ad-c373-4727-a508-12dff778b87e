import { useEffect, useCallback, useState, useRef } from "react";

import { useEdit<PERSON>, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Underline from "@tiptap/extension-underline";
import Strike from "@tiptap/extension-strike";
import Blockquote from "@tiptap/extension-blockquote";
import CodeBlock from "@tiptap/extension-code-block";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import Heading from "@tiptap/extension-heading";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import Color from "@tiptap/extension-color";
import Table from "@tiptap/extension-table";
import TextAlign from "@tiptap/extension-text-align";
import { MathExtension } from "@aarkue/tiptap-math-extension";
import "katex/dist/katex.min.css";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import { Eye, Edit } from "lucide-react";

import MenuBar from "@/components/commons/Editor/components/MenuBar/MenuBar";
import TableToolbar from "@/components/commons/Editor/components/TableToolbar/TableToolbar";
import LinkPopover from "@/components/commons/Editor/components/LinkPopover/LinkPopover";
import CustomTableCell from "@/components/commons/Editor/components/extentions/CustomTableCell";

import "./Editor.scss";
import { convertLatexInDoc } from "./utils";

interface EditorProps {
  value?: string;
  onChange?: (v: string) => void;
}

const Editor = ({ value = "", onChange }: EditorProps) => {
  const [preview, setPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [showLinkPopover, setShowLinkPopover] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [showTableControls, setShowTableControls] = useState(false);

  const editor = useEditor(
    {
      extensions: [
        StarterKit as any,
        Underline,
        Strike,
        Blockquote,
        CodeBlock,
        Subscript,
        Superscript,
        ListItem,
        OrderedList,
        Heading.configure({ levels: [1, 2, 3] }),
        Link.configure({
          openOnClick: false, // Prevents opening link on click, allowing selection
          autolink: true, // Auto-detect links
        }),
        Image,
        Color,
        TextAlign.configure({ types: ["heading", "paragraph"] }),
        MathExtension.configure({
          evaluation: true,
          addInlineMath: true,
        }),
        Placeholder.configure({
          placeholder: "Write something...",
        }),
        Table.configure({
          resizable: true,
        }),
        TableRow,
        TableHeader,
        CustomTableCell,
      ],
      content: value,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        onChange?.(html);
        // Check if the cursor is inside a table for showing controls
        setShowTableControls(editor.isActive("table"));
      },
      onSelectionUpdate: ({ editor }) => {
        // Also update on selection changes
        setShowTableControls(editor.isActive("table"));
      },
      editable: !preview,
    },
    [preview],
  );

  const handleImageUpload = useCallback((file: File) => {
    const imageUrl = URL.createObjectURL(file);
    editor?.chain().focus().setImage({ src: imageUrl }).run();
  }, []); // Depend on editor if it's not stable or passed as a prop

  const handleLinkSubmit = useCallback(() => {
    if (linkUrl) {
      editor?.chain().focus().setLink({ href: linkUrl }).run();
      setShowLinkPopover(false);
      setLinkUrl("");
    }
  }, [editor, linkUrl]);

  const handleLinkIconClick = useCallback(() => {
    const previousUrl = editor?.getAttributes("link").href;
    setLinkUrl(previousUrl || "");
    setShowLinkPopover(true);
  }, [editor]);

  useEffect(() => {
    if (editor) {
      // Initially check if a table is active when the component mounts or editor is ready
      setShowTableControls(editor.isActive("table"));
    }
  }, [editor]);

  useEffect(() => {
    if (editor && value) {
      // Set the content as plain text
      editor.commands.setContent(value, false); // false = not HTML
      // Convert LaTeX patterns to math nodes
      convertLatexInDoc(editor);
    }
  }, [editor]);

  if (!editor) return <p>Loading editor...</p>;

  return (
    <div className="custom-editor space-y-2 rounded border p-4">
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            handleImageUpload(file);
          }
        }}
      />

      {/* Link Popover */}
      <LinkPopover
        show={showLinkPopover}
        url={linkUrl}
        onUrlChange={(e) => setLinkUrl(e.target.value)}
        onSubmit={handleLinkSubmit}
        onCancel={() => {
          setShowLinkPopover(false);
          setLinkUrl("");
        }}
      />

      {/* Preview toggle */}
      <div className="mb-2 flex justify-end">
        <button
          onClick={() => setPreview((p) => !p)}
          className="flex items-center gap-1 rounded border px-3 py-1 text-sm hover:bg-gray-100"
        >
          {preview ? <Edit size={16} /> : <Eye size={16} />}
          {preview ? "Edit" : "Preview"}
        </button>
      </div>

      {!preview && !!editor && (
        <>
          <MenuBar
            editor={editor}
            onImageUploadClick={() => fileInputRef.current?.click()}
            onLinkIconClick={handleLinkIconClick}
          />
          {showTableControls && <TableToolbar editor={editor} />}
        </>
      )}
      <EditorContent editor={editor} />
    </div>
  );
};

export default Editor;

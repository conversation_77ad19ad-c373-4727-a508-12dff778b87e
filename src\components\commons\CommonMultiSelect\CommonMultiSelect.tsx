import React, { FC, useMemo, useState } from "react";

import { EIconName, EIconSize } from "@/components/commons/Icons/Icon.enums";
import Icon from "@/components/commons/Icons/Icon";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import ButtonCTA, {
  EButtonType,
} from "@/components/commons/ButtonCTA/ButtonCTA";
import { EColor } from "@/constants/colors";
import { cn } from "@/lib/utils";

import { ICommonMultiSelectProps } from "./CommonMultiSelect.d";

const CommonMultiSelect: FC<ICommonMultiSelectProps> = ({
  options,
  customTrigger,
  title,
  classNameMenu,
}) => {
  const [currentValue, setCurrentValue] = useState<string[]>([]);

  const isCheckAll = useMemo(() => {
    return options.every((item) => currentValue.includes(item.value));
  }, [currentValue, options]);

  const handleOnChange = (valueOption: string) => {
    if (!valueOption) return;
    const isExist = currentValue.findIndex((item) => item === valueOption);
    if (isExist > -1) {
      return setCurrentValue((pre) =>
        pre.filter((item) => item !== valueOption),
      );
    }
    return setCurrentValue((pre) => [...pre, valueOption]);
  };

  const handleCheckAll = () => {
    if (isCheckAll) {
      return setCurrentValue([]);
    }
    return setCurrentValue(options.map((item) => item.value));
  };

  const handleClear = () => {
    setCurrentValue([]);
  };

  const iconCheckAll = () => {
    if (isCheckAll) {
      return EIconName.CHECK_SQUARE;
    }
    if (!isCheckAll && currentValue.length) {
      return EIconName.MINUS_SQUARE_FULL;
    }
    return EIconName.SQUARE_FULL;
  };

  return (
    <Popover>
      <PopoverTrigger>{customTrigger || <div>{title}</div>}</PopoverTrigger>
      <PopoverContent className={cn("w-max min-w-[180px] p-0", classNameMenu)}>
        <div className="flex items-center justify-between border-b p-[8px]">
          <div
            className="flex cursor-pointer items-center justify-start gap-[8px]"
            onClick={() => handleCheckAll()}
          >
            <Icon color={EColor.TARAWERA} name={iconCheckAll()} />
            <span> All </span>
          </div>
          <ButtonCTA onClick={handleClear} variant={EButtonType.OUTLINE}>
            Clear
          </ButtonCTA>
        </div>
        <div className="p-[8px]">
          {options.map((option) => {
            const { label, value } = option;
            const isChecked =
              currentValue.findIndex((item) => item === value) > -1;
            return (
              <div
                onClick={() => handleOnChange(value)}
                className="flex cursor-pointer items-center justify-start gap-[8px]"
                key={value}
              >
                <Icon
                  size={EIconSize.MD}
                  color={EColor.TARAWERA}
                  name={
                    isChecked ? EIconName.CHECK_SQUARE : EIconName.SQUARE_FULL
                  }
                />
                <span className="text-[14px] font-[500] text-primary">
                  {label}
                </span>
              </div>
            );
          })}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default CommonMultiSelect;

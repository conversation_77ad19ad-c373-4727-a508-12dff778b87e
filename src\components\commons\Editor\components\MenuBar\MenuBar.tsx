import { FC } from "react";

import { TMenuBarProps } from "./MenuBar.d";

import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  Quote,
  Code,
  List,
  ListOrdered,
  Subscript as SubIcon,
  Superscript as SuperIcon,
  Heading1,
  Heading2,
  Heading3,
  Link as LinkIcon,
  Image as ImageIcon,
  Sigma,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Table as TableIcon,
} from "lucide-react";

export const IconButton = ({
  icon,
  action,
  active = false,
  title = "",
  disabled = false,
}: {
  icon: React.ReactNode;
  action: () => void;
  active?: boolean;
  title?: string;
  disabled?: boolean;
}) => (
  <button
    title={title}
    onClick={action}
    className={`rounded p-1 hover:bg-gray-200 ${
      active ? "bg-gray-300 text-blue-600" : ""
    } ${disabled ? "cursor-not-allowed opacity-50" : ""}`}
    disabled={disabled}
  >
    {icon}
  </button>
);

const MenuBar: FC<TMenuBarProps> = ({
  editor,
  onImageUploadClick,
  onLinkIconClick,
}) => {
  if (!editor) {
    return null;
  }

  return (
    <div className="mb-2 flex flex-wrap items-center gap-2 border-b border-gray-300 pb-2">
      <IconButton
        icon={<Bold size={16} />}
        action={() => editor.chain().focus().toggleMark("bold").run()}
        active={editor.isActive("bold")}
        title="Bold"
      />
      <IconButton
        icon={<Italic size={16} />}
        action={() => editor.chain().focus().toggleMark("italic").run()}
        active={editor.isActive("italic")}
        title="Italic"
      />
      <IconButton
        icon={<UnderlineIcon size={16} />}
        action={() => editor.chain().focus().toggleMark("underline").run()}
        active={editor.isActive("underline")}
        title="Underline"
      />
      <IconButton
        icon={<Strikethrough size={16} />}
        action={() => editor.chain().focus().toggleStrike().run()}
        active={editor.isActive("strike")}
        title="Strikethrough"
      />
      <IconButton
        icon={<Quote size={16} />}
        action={() => editor.chain().focus().toggleBlockquote().run()}
        active={editor.isActive("blockquote")}
        title="Blockquote"
      />
      <IconButton
        icon={<Code size={16} />}
        action={() => editor.chain().focus().toggleCodeBlock().run()}
        active={editor.isActive("codeBlock")}
        title="Code Block"
      />
      <IconButton
        icon={<List size={16} />}
        action={() => (editor.chain().focus() as any).toggleBulletList().run()}
        active={editor.isActive("bulletList")}
        title="Bullet List"
      />
      <IconButton
        icon={<ListOrdered size={16} />}
        action={() => editor.chain().focus().toggleOrderedList().run()}
        active={editor.isActive("orderedList")}
        title="Ordered List"
      />
      <IconButton
        icon={<SubIcon size={16} />}
        action={() => editor.chain().focus().toggleSubscript().run()}
        active={editor.isActive("subscript")}
        title="Subscript"
      />
      <IconButton
        icon={<SuperIcon size={16} />}
        action={() => editor.chain().focus().toggleSuperscript().run()}
        active={editor.isActive("superscript")}
        title="Superscript"
      />

      <IconButton
        icon={<Heading1 size={16} />}
        action={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        active={editor.isActive("heading", { level: 1 })}
        title="Heading 1"
      />
      <IconButton
        icon={<Heading2 size={16} />}
        action={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        active={editor.isActive("heading", { level: 2 })}
        title="Heading 2"
      />
      <IconButton
        icon={<Heading3 size={16} />}
        action={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
        active={editor.isActive("heading", { level: 3 })}
        title="Heading 3"
      />

      <IconButton
        icon={<AlignLeft size={16} />}
        action={() => editor.chain().focus().setTextAlign("left").run()}
        active={editor.isActive({ textAlign: "left" })}
        title="Align Left"
      />
      <IconButton
        icon={<AlignCenter size={16} />}
        action={() => editor.chain().focus().setTextAlign("center").run()}
        active={editor.isActive({ textAlign: "center" })}
        title="Align Center"
      />
      <IconButton
        icon={<AlignRight size={16} />}
        action={() => editor.chain().focus().setTextAlign("right").run()}
        active={editor.isActive({ textAlign: "right" })}
        title="Align Right"
      />
      <IconButton
        icon={<AlignJustify size={16} />}
        action={() => editor.chain().focus().setTextAlign("justify").run()}
        active={editor.isActive({ textAlign: "justify" })}
        title="Justify"
      />

      <IconButton
        icon={<LinkIcon size={16} />}
        action={onLinkIconClick}
        active={editor.isActive("link")}
        title="Insert Link"
      />
      <IconButton
        icon={<ImageIcon size={16} />}
        action={onImageUploadClick}
        title="Insert Image"
      />
      <IconButton
        icon={<Sigma size={16} />}
        action={() =>
          editor
            .chain()
            .focus()
            .insertContent({
              type: "mathInline",
              attrs: {
                value: "\\begin{bmatrix} 1 & 2 \\\\ 3 & 4 \\end{bmatrix}",
                display: "yes",
              },
            })
            .run()
        }
        title="Insert LaTeX Formula"
      />
      <IconButton
        icon={<TableIcon size={16} />}
        action={() =>
          editor
            .chain()
            .focus()
            .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
            .run()
        }
        title="Insert Table"
      />
    </div>
  );
};

export default MenuBar;

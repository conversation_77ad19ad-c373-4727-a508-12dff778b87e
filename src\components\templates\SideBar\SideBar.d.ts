import { EIconName } from "@/components/commons/Icons/Icon.enums";

export type SideBarMenuIconProps = {
  iconName?: EIconName | string;
  isActive: boolean;
  to?: string;
  title: string;
  isParent?: boolean;
};

export interface SubMenu {
  title: string;
  to: string;
  iconName?: string | EIconName;
}

interface SideBarMenuItemProps {
  title: string;
  to?: string;
  iconName?: EIconName | string;
  parent?: { title: string; to: string };
  children?: React.ReactNode | undefined;
  index?: string;
  parentId?: string;
  subMenus?: SubMenu[];
}

export type SideBarMenuItemLinkProps = {
  to?: string;
  title: string;
  shortTitle?: string;
  // eslint-disable-next-line @typescript-eslint/ban-types
  onToggle: Function;
  parent?: { title: string; to: string };
  active?: boolean;
  menuId?: string;
  parentId?: string | undefined;
  iconName?: EIconName | string;
  subMenus?: SubMenu[];
};

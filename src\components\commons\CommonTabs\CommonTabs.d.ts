import { ReactNode } from "react";

export enum E_TAB_TYPE {
  DEFAULT = "default",
  TAB_CARD = "tab_card",
}

export interface ITab {
  tabName: string | ReactNode;
  tabKey: string;
  component: string | ReactNode;
  className?: string;
  classNameTabList?: string;
}

export interface ICommonTabsProps {
  tabs: ITab[];
  activeTabKey?: string;
  defaultTabKey?: string;
  isSticky?: boolean;
  onChange: (value: string) => void;
  className?: string;
  classNameTabList?: string;
  type?: E_TAB_TYPE;
}

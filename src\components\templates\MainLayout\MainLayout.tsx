/* eslint-disable react-hooks/exhaustive-deps */
"use client";
import { ReactNode } from "react";

import Header from "@/components/templates/Header/Header";
import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "next/navigation";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";

interface MainLayoutProps {
  children: ReactNode;
  className?: string;
}

// This is the place responsible for wrapping your app.
// Add here components like Footer, Nav etc.
export const MainLayout = ({ children, className }: MainLayoutProps) => {
  const wrapperStyles = cn("flex min-h-screen w-screen flex-row", className);
  // const pathname = usePathname();
  // const router = useRouter();
  // const logout = useAuthStore((state) => state.logout);
  // const access_token = useAuthStore((state) => state.access_token);

  // useEffect(() => {
  //   if (!NO_LAYOUT.includes(pathname) && access_token === "") {
  //     logout();
  //     router.push("/login");
  //   }
  // }, [access_token, pathname]);

  return (
    <div className={wrapperStyles}>
      <div className="flex w-full flex-1 flex-col">
        <Header />
        <main className="flex-grow bg-[#F0F2F4]">{children}</main>
        {/* <footer className="flex items-center justify-center p-4">
          ©
          <Link
            href="https://backoffice.staging.miva.university/"
            className="pr-2"
          >
            Miva Development Team
          </Link>
          Copyright {new Date().getFullYear()}
        </footer> */}
      </div>
    </div>
  );
};

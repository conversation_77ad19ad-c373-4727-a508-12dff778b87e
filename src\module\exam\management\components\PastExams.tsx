import ExamCards from "./ExamCards";
import { TabPanel } from "@chakra-ui/react";
import { ExamCard } from "../ExamManagement";
import React, { useEffect, useState } from "react";
import { useGetPastExams } from "@/hooks/useExamManagement/useGetPastExams";
import { Paginated } from "@/api/config/api.d";

const PastExams = () => {
  const [pastExams, setPastExams] = useState<Paginated<ExamCard[]>>({
    currentPage: 1,
    data: [],
    nextPage: 1,
    perPage: 20,
    prevPage: 0,
    total: 0,
    totalPages: 0,
  });
  const { isLoading, data, refetch, isFetching } = useGetPastExams(
    pastExams.nextPage,
    pastExams.perPage,
  );
  useEffect(() => {
    if (data && !isFetching) {
      setPastExams((prev) => ({
        ...data.meta,
        data: [...prev.data, ...data.examWithTag],
      }));
    }
  }, [isFetching]);

  return (
    <TabPanel>
      <ExamCards
        data={pastExams.data}
        fetch={refetch}
        hasMore={data?.meta.currentPage !== data?.meta.nextPage}
        loading={isLoading}
      />
    </TabPanel>
  );
};

export default PastExams;

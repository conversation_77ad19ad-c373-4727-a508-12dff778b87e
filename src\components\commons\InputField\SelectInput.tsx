import { BaseColor } from "@/constants/colors";
import { Stack, SelectProps, Text } from "@chakra-ui/react";
import { SelectOption } from "./SelectInput.d";
import CommonSelectWithSearch from "../CommonSelectWithSearch/CommonSelectWithSearch";
import { ChangeEvent } from "react";

const SelectInput = ({
  label,
  required,
  ...props
}: SelectProps & {
  options?: SelectOption[];
  label?: string | undefined;
  required?: boolean;
  defaultOption?: string;
}) => {
  return (
    <Stack>
      {!!label && (
        <Text fontWeight="600" color={BaseColor.PRIMARY} fontSize="14px">
          {label}{" "}
          {required && (
            <Text as="span" color="red.500">
              *
            </Text>
          )}
        </Text>
      )}
      <CommonSelectWithSearch
        {...props}
        showIndicatorIcon={true}
        options={props?.options || []}
        className={`!border-1 h-12 w-full text-[14px] shadow-none focus:ring-2 focus:ring-[#3182ce]`}
        onChange={(value: string) => {
          props.onChange?.({
            target: { name: props.name, value },
          } as ChangeEvent<HTMLSelectElement>);
        }}
        autoSelectFirstOption={false}
      />
    </Stack>
  );
};

export default SelectInput;

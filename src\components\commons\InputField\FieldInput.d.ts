import { ReactNode } from "react";
import { E_FIELD_INPUT_TYPE } from "@/constants/enums";

export interface IFieldInputProps {
  name: string;
  label?: string;
  placeholder?: string;
  className?: string;
  isRequire?: boolean;
  isDisabled?: boolean;
  maxLength?: number;
  valueField: string | number;
  prefixIcon?: string | ReactNode;
  type?: E_FIELD_INPUT_TYPE;
  onChange?: (value: string | number) => void;
}

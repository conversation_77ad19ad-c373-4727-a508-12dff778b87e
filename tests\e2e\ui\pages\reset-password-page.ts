import { type Page, type Locator, expect } from "@playwright/test";
import { apiLogin } from "tests/e2e/utils/api-utils";
import { getVerificationCode } from "tests/e2e/utils/email-utils";

class ResetPasswordPage {
  readonly page: Page;
  readonly forgotPasswordLink: Locator;
  readonly emailField: Locator;
  readonly resetPasswordButton: Locator;
  readonly continueButton: Locator;
  readonly tokenField: Locator;
  readonly passwordField: Locator;
  readonly confirmPasswordField: Locator;
  readonly passwordResetSuccessToast: Locator;

  constructor(page: Page) {
    this.page = page;
    this.forgotPasswordLink = page.getByText("Forgot Password?");
    this.emailField = page.getByPlaceholder("Email Address");
    this.resetPasswordButton = page.getByRole("button", {
      name: "Reset password",
    });
    this.continueButton = page.getByRole("button", { name: "Continue" });
    this.tokenField = page.getByPlaceholder("Enter reset token");
    this.passwordField = page.getByPlaceholder("Create your password");
    this.confirmPasswordField = page.getByPlaceholder("Confirm your password");
    this.passwordResetSuccessToast = page.getByText(
      "Password reset successful",
    );
  }

  private async fillEmail(email: string) {
    await this.emailField.fill(email);
  }

  private async fillToken(verificationCode: string) {
    await this.tokenField.fill(verificationCode);
  }

  private async fillPassword(password: string) {
    await this.passwordField.fill(password);
  }

  private async fillConfirmPassword(confirmPassword: string) {
    await this.confirmPasswordField.fill(confirmPassword);
  }

  async resetPassword(
    email: string,
    password: string,
    confirmPassword: string,
  ) {
    // await this.forgotPasswordLink.click();
    await this.fillEmail(email);
    await this.resetPasswordButton.click();
    await this.continueButton.click();
    await this.page.waitForTimeout(3000);
    const verificationCode = await getVerificationCode(
      email,
      "resetPasswordRequest",
    );
    await this.fillToken(verificationCode);
    await this.fillPassword(password);
    await this.fillConfirmPassword(confirmPassword);
    await this.resetPasswordButton.click();
  }

  async assertPasswordResetSuccess() {
    await expect(this.passwordResetSuccessToast).toBeVisible();
  }

  async assertUserCanLoginWithNewPassword(email: string, password: string) {
    const body = await apiLogin(email, password);
    expect(body.data).toHaveProperty("access_token");
  }
}
export default ResetPasswordPage;

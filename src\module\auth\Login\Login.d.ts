export interface ILoginValue {
  email: string;
  password: string;
}

export interface ILoginVerifyValue {
  code: string;
}

export interface ILoginContainerProps {
  handleLogin: (value: ILoginValue) => Promise<void>;
}

export interface ILoginVerifyResponse {
  access_token: string;
}
export interface ILoginResponse {
  access_token?: string;
}
export interface ILoginVerifyContainerProps {
  handleLoginVerify: (value: ILoginVerifyValue) => Promise<void>;
}

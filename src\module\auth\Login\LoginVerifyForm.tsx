import { FormikProps } from "formik";
import { useRouter } from "next/navigation";
import { Flex, Text, Stack, Box } from "@chakra-ui/react";

import HeaderForm from "../components/HeaderForm";
import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import InputField from "@/components/commons/InputField/InputField";

import { ILoginVerifyValue } from "./Login";
import { BaseColor } from "@/constants/colors";
import { InputError } from "@/components/commons/InputField/InputError";

const LoginVerifyForm = (props: FormikProps<ILoginVerifyValue>) => {
  const router = useRouter();
  const {
    touched,
    errors,
    handleSubmit,
    handleChange,
    handleBlur,
    values,
    isSubmitting,
  } = props;
  const { code: codeTouched } = touched;
  const { code: codeError } = errors;

  const handleDidNotReceive = () => {
    router.push("/login");
  };

  return (
    <Flex
      m={{ base: "20px" }}
      flexDir="column"
      width={{ md: "400px", base: "320px" }}
    >
      <HeaderForm
        title="Verify Log In"
        subTitle="Enter your verification code"
      />
      <Stack mt="48px" spacing="16px">
        <Box>
          <InputField
            placeholder="Code"
            borderColor={BaseColor.PRIMARY_400}
            color="white"
            _placeholder={{ color: BaseColor.PRIMARY_400 }}
            name="code"
            onChange={handleChange}
            onBlur={handleBlur}
            value={values.code}
          />
          <InputError error={codeError} touched={codeTouched} />
        </Box>
      </Stack>
      <Box textAlign="right">
        <Text
          cursor="pointer"
          onClick={handleDidNotReceive}
          mt="12px"
          fontSize="14px"
          color="white"
          display="inline-block"
        >
          Did not receive code?
        </Text>
      </Box>

      <Stack mt="48px">
        <ButtonCTA
          isLoading={isSubmitting}
          onClick={() => {
            handleSubmit();
          }}
        >
          Verify
        </ButtonCTA>
      </Stack>
    </Flex>
  );
};

export default LoginVerifyForm;

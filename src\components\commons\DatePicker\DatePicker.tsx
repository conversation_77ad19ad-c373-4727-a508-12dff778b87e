import { Calendar } from "@/components/ui/calendar";
import { FC, useMemo, useState } from "react";
import { CalendarIcon } from "lucide-react";

import { IDatePickerProps } from "./DatePicker.d";
import { format, isValid } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const startYear = 1900;
const endYear = new Date().getFullYear() + 10;
const yearOptions = Array.from(
  { length: endYear - startYear + 1 },
  (_, index) => startYear + index,
);

const DatePicker: FC<IDatePickerProps> = ({ date, onChange, disabled }) => {
  const [month, setMonth] = useState(date ? new Date(date) : undefined);
  const [open, setOpen] = useState<boolean>(false);

  const currentDate = useMemo(() => {
    if (isValid(new Date(date))) {
      setMonth(new Date(date));
      return new Date(date);
    }
    return undefined;
  }, [date]);

  const handleYearChange = (value: string) => {
    const newYear = parseInt(value, 10);
    const updatedDate = new Date(date);
    updatedDate.setFullYear(newYear);
    onChange(updatedDate);
  };

  const handleDateSelect = (selectedDate: Date | undefined) => {
    onChange(selectedDate);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          onClick={() => setOpen(!open)}
          disabled={disabled}
          variant={"outline"}
          className={cn(
            "flex h-[48px] w-full items-center justify-start gap-[4px] pl-3 text-left font-normal",
            !currentDate && "text-muted-foreground",
          )}
        >
          <CalendarIcon className="h-4 w-4 opacity-50" />
          {currentDate ? (
            format(currentDate, "yyyy/MM/dd")
          ) : (
            <span>YYYY/MM/DD</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Select
          value={currentDate?.getFullYear()?.toString()}
          onValueChange={handleYearChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select year" />
          </SelectTrigger>
          <SelectContent position="popper">
            {yearOptions.map((year) => (
              <SelectItem key={year} value={year?.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Calendar
          month={month}
          onMonthChange={(monthChange) => setMonth(monthChange)}
          mode="single"
          selected={currentDate}
          onSelect={handleDateSelect}
          className="rounded-md"
        />
      </PopoverContent>
    </Popover>
  );
};

export default DatePicker;

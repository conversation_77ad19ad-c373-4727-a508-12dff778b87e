import React from "react";
import { cn } from "@/lib/utils";

import { EColor } from "@/constants/colors";
import {
  EIconName,
  EIconSize,
  TypeIcon,
} from "@/components/commons/Icons/Icon.enums";
import Icon from "@/components/commons/Icons/Icon";
import { useSideBarStore } from "../../../store/SideBarStore/sideBar";

import { SideBarMenuIconProps } from "./SideBar.d";

const SidebarIcon: React.FC<SideBarMenuIconProps> = ({
  iconName,
  title,
  to,
  isActive = false,
}) => {
  const isExpand = useSideBarStore((state) => state.isExpandSideBar);
  return (
    <div className="side-bar-icon-wrapper flex w-full items-center px-4 py-2">
      {iconName && (
        <Icon
          isStaticIcon={true}
          color={EColor.MINE_SHAFT}
          name={iconName as EIconName}
          size={EIconSize.MD}
          onClick={() => {}}
          type={TypeIcon.SVG}
          selectedIcon={{
            name: iconName as EIconName,
            backgroundColor: EColor.TRANSPARENT,
          }}
          isSelected={isActive}
        />
      )}

      <div
        className={cn(
          "menu-headline-group flex w-full items-center justify-between overflow-x-hidden",
          { active: isActive },
        )}
      >
        {isExpand && <span className="menu-headline text-nowrap">{title}</span>}
        {!to && isExpand && (
          <Icon
            className={cn("menu-headline-dropdown")}
            color={EColor.MINE_SHAFT}
            name={EIconName.CHEVRON_RIGHT}
            size={EIconSize.SM}
            type={TypeIcon.SVG}
            onClick={() => {}}
            isStaticIcon={true}
            isSelected={isActive}
          />
        )}
      </div>
    </div>
  );
};

export default SidebarIcon;

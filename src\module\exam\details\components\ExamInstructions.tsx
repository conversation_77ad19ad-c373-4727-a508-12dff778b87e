import { BaseColor } from "@/constants/colors";
import { HStack, VStack, Text } from "@chakra-ui/react";
import React from "react";

export interface IExamInstructionProps {
  sittingMode: string;
  isStudentAtNigeria: boolean;
}

const ExamInstructions = ({
  sittingMode,
  isStudentAtNigeria,
}: IExamInstructionProps) => {
  const isExamCenterMode = sittingMode === "ONLINE_AND_IN_PERSON";
  return (
    <>
      <Text
        fontSize="24px"
        fontWeight="bold"
        textAlign="center"
        className="mx-auto my-4"
      >
        Instructions
      </Text>
      <VStack
        px="26px"
        py="32px"
        bg={BaseColor.PRIMARY_200}
        maxWidth="970px"
        mx="auto"
        mb="40px"
        alignItems="start"
      >
        <HStack>
          <Text>•</Text>
          <Text color={BaseColor.PRIMARY} fontSize="16px" className="flex-1">
            Make sure your device is connected to a reliable network.
            Disconnection may disrupt your exam.
          </Text>
        </HStack>
        <HStack>
          <Text>•</Text>
          <Text color={BaseColor.PRIMARY} fontSize="16px" className="flex-1">
            You can move between questions freely by using the{" "}
            <strong>&quot;Next&quot;</strong>
            and <strong>&quot;Previous&quot;</strong> buttons. Be sure to review
            your answers before submission.
          </Text>
        </HStack>
        <HStack>
          <Text>•</Text>
          <Text color={BaseColor.PRIMARY} fontSize="16px" className="flex-1">
            The exam will be auto-submitted when the time limit is reached.
            Please manage your time accordingly.
          </Text>
        </HStack>
        {(!isExamCenterMode || !isStudentAtNigeria) && (
          <HStack>
            <Text>•</Text>
            <Text color={BaseColor.PRIMARY} fontSize="16px" className="flex-1">
              You need to <strong>Download</strong> the SEB before proceeding to
              the exam.
            </Text>
          </HStack>
        )}{" "}
        <HStack>
          <Text>•</Text>
          <Text color={BaseColor.PRIMARY} fontSize="16px" className="flex-1">
            This exam is being monitored. Any attempts to cheat or violate exam
            rules will result in disqualification or disciplinary actions.
          </Text>
        </HStack>
        <HStack>
          <Text>•</Text>
          <Text color={BaseColor.PRIMARY} fontSize="16px" className="flex-1">
            Once you click <strong>&quot;Start Exam&quot;</strong>, the timer
            will begin, and you&apos;ll need to complete the exam in one
            sitting.
          </Text>
        </HStack>
      </VStack>
    </>
  );
};

export default ExamInstructions;

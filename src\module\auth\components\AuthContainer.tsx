import { BaseColor } from "@/constants/colors";
import { Box, Flex } from "@chakra-ui/react";

export const AuthContainer = ({
  children,
}: {
  children: React.ReactNode;
}): React.ReactElement => {
  return (
    <Box
      height="100%"
      className="flex justify-center"
      bg={BaseColor.PRIMARY_700}
    >
      <Flex
        alignItems="center"
        gap={{ base: "20px", xl: "140px" }}
        w="100%"
        justifyContent={{ base: "space-around", xl: "center" }}
      >
        {children}
      </Flex>
    </Box>
  );
};

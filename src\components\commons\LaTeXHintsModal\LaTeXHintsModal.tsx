"use client";

import React from "react";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Box,
  Code,
  useToast,
} from "@chakra-ui/react";
import { CommonModal } from "@/components/commons/CommonModal/CommonModal";

interface LaTeXHintsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LaTeXHintsModal: React.FC<LaTeXHintsModalProps> = ({
  isOpen,
  onClose,
}) => {
  const toast = useToast();

  const latexExamples = [
    {
      function: "Trigonometric",
      latexSyntax: "\\sin(x), \\cos(x)",
      example: "f(x) = \\sin(x) + \\cos(x)",
      view: "f(x) = sin(x) + cos(x)",
    },
    {
      function: "Logarithms",
      latexSyntax: "\\ln(x), \\log_{base}(x)",
      example: "f(x) = \\ln(x), \\quad g(x) = \\log_{10}(x)",
      view: "f(x) = ln(x), g(x) = log₁₀(x)",
    },
    {
      function: "Logarithms",
      latexSyntax: "*",
      example: "4*3 = 12",
      view: "4*3 = 12",
    },
    {
      function: "Exponent",
      latexSyntax: "^",
      example: "x^2 + y^2 = z^2",
      view: "x² + y² = z²",
    },
    {
      function: "Square Root",
      latexSyntax: "\\sqrt{expression}",
      example: "\\sqrt{25} = 5",
      view: "√25 = 5",
    },
    {
      function: "nth Root",
      latexSyntax: "\\sqrt[n]{expression}",
      example: "\\sqrt[3]{27} = 3",
      view: "∛27 = 3",
    },
    {
      function: "Summation",
      latexSyntax: "\\sum_{lower limit}^{upper limit} expression",
      example: "\\sum_{i=1}^{n} i",
      view: "∑ᵢ₌₁ⁿ i",
    },
    {
      function: "Product",
      latexSyntax: "\\prod_{k=1}^{n} f_k \\cdot k = n!",
      example: "\\prod_{k=1}^{n} f_k \\cdot k = n!",
      view: "∏ₖ₌₁ⁿ fₖ · k = n!",
    },
    {
      function: "Matrix",
      latexSyntax: "\\begin{bmatrix}...\\end{bmatrix}",
      example: "\\begin{bmatrix} 1 & 2 \\\\ 3 & 4 \\end{bmatrix}",
      view: "[1 2; 3 4]",
    },
    {
      function: "Integral",
      latexSyntax:
        "\\int_{lower limit}^{upper limit} expression \\, d{variable}",
      example: "\\int_0^1 x^2 \\, dx = \\frac{1}{3}",
      view: "∫₀¹ x² dx = 1/3",
    },
    {
      function: "Derivative",
      latexSyntax: "\\frac{d}{dx} (expression)",
      example: "\\frac{d}{dx} x^3 = 3x^2",
      view: "d/dx (x³) = 3x²",
    },
  ];

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast({
          title: "Copied to clipboard!",
          description: "LaTeX code has been copied and ready to paste.",
          status: "success",
          duration: 2000,
          isClosable: true,
          position: "top",
        });
      })
      .catch(() => {
        toast({
          title: "Copy failed",
          description: "Unable to copy to clipboard. Please try again.",
          status: "error",
          duration: 3000,
          isClosable: true,
          position: "top",
        });
      });
  };

  return (
    <CommonModal
      isOpen={isOpen}
      onClose={onClose}
      title="Hints on Using The Editor for Formulas"
      wideContent={true}
      size="6xl"
      scrollBehavior="inside"
      closeOnOverlayClick={true}
      closeOnEsc={true}
    >
      <Box px={6} pb={6} maxHeight="70vh" overflowY="auto">
        <Box mb={4}>
          <Text
            fontSize="14px"
            color="#E83831"
            fontWeight="medium"
            textAlign="center"
          >
            Type a &quot;$&quot; at the <strong>start</strong> and{" "}
            <strong>end</strong> of your equation to format correctly.{" "}
            <Text as="span" color="#3B5A73">
              eg. Equation$
            </Text>
          </Text>
        </Box>

        <Box overflowX="auto" maxHeight="50vh" overflowY="auto">
          <Table
            variant="simple"
            size="sm"
            role="table"
            aria-label="LaTeX syntax examples"
          >
            <Thead>
              <Tr backgroundColor="#E9ECEF">
                <Th
                  fontSize="16px"
                  fontWeight="semibold"
                  color="#3B5A73"
                  py={3}
                  scope="col"
                >
                  Function
                </Th>
                <Th
                  fontSize="16px"
                  fontWeight="semibold"
                  color="#3B5A73"
                  py={3}
                  scope="col"
                >
                  LaTeX Syntax
                </Th>
                <Th
                  fontSize="16px"
                  fontWeight="semibold"
                  color="#3B5A73"
                  py={3}
                  scope="col"
                >
                  Example
                </Th>
                <Th
                  fontSize="16px"
                  fontWeight="semibold"
                  color="#3B5A73"
                  py={3}
                  scope="col"
                >
                  View
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {latexExamples.map((item, index) => (
                <Tr
                  key={index}
                  borderBottom="1px solid #E2E8F0"
                  backgroundColor={index % 2 === 0 ? "white" : "#F0F2F4"}
                >
                  <Td fontSize="14px" fontWeight="500" color="#3B5A73" py={3}>
                    {item.function}
                  </Td>
                  <Td fontSize="14px" py={3}>
                    <Code
                      fontSize="14px"
                      backgroundColor="#F7F9FC"
                      color="#3B5A73"
                      px={2}
                      py={1}
                      borderRadius="4px"
                      cursor="pointer"
                      _hover={{ backgroundColor: "#E2E8F0" }}
                      onClick={() => handleCopyToClipboard(item.latexSyntax)}
                      title="Click to copy LaTeX syntax"
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          e.preventDefault();
                          handleCopyToClipboard(item.latexSyntax);
                        }
                      }}
                    >
                      {item.latexSyntax}
                    </Code>
                  </Td>
                  <Td fontSize="14px" py={3}>
                    <Code
                      fontSize="14px"
                      backgroundColor="#F7F9FC"
                      color="#3B5A73"
                      px={2}
                      py={1}
                      borderRadius="4px"
                    >
                      {item.example}
                    </Code>
                  </Td>
                  <Td fontSize="14px" color="#3B5A73" py={3}>
                    {item.view}
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      </Box>
    </CommonModal>
  );
};

export default LaTeXHintsModal;

"use client";

import React from "react";
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Box,
  Code,
} from "@chakra-ui/react";
import { CommonModal } from "@/components/commons/CommonModal/CommonModal";

interface LaTeXHintsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LaTeXHintsModal: React.FC<LaTeXHintsModalProps> = ({
  isOpen,
  onClose,
}) => {
  const latexExamples = [
    {
      function: "Trigonometric",
      latexSyntax: "\\sin(x), \\cos(x)",
      example: "f(x) = \\sin(x) + \\cos(x)",
      view: "f(x) = sin(x) + cos(x)",
    },
    {
      function: "Logarithms",
      latexSyntax: "\\ln(x), \\log_{base}(x)",
      example: "f(x) = \\ln(x), \\quad g(x) = \\log_{10}(x)",
      view: "f(x) = ln(x), g(x) = log₁₀(x)",
    },
    {
      function: "Logarithms",
      latexSyntax: "^",
      example: "x^2 = 12",
      view: "x² = 12",
    },
    {
      function: "Exponent",
      latexSyntax: "^",
      example: "x^2 + y^2 = z^2",
      view: "x² + y² = z²",
    },
    {
      function: "Square Root",
      latexSyntax: "\\sqrt{expression}",
      example: "\\sqrt{25} = 5",
      view: "√25 = 5",
    },
    {
      function: "nth Root",
      latexSyntax: "\\sqrt[n]{expression}",
      example: "\\sqrt[3]{27} = 3",
      view: "∛27 = 3",
    },
    {
      function: "Summation",
      latexSyntax: "\\sum_{lower limit}^{upper limit} expression",
      example: "\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}",
      view: "∑ᵢ₌₁ⁿ i = n(n+1)/2",
    },
    {
      function: "Product",
      latexSyntax: "\\prod_{k=1}^{n} f_k \\cdot k = n!",
      example: "\\prod_{k=1}^{n} f_k \\cdot k = n!",
      view: "∏ₖ₌₁ⁿ fₖ · k = n!",
    },
    {
      function: "Matrix",
      latexSyntax: "\\begin{bmatrix}...\\end{bmatrix}",
      example: "\\begin{bmatrix} 1 & 2 \\\\ 3 & 4 \\end{bmatrix}",
      view: "[1 2; 3 4]",
    },
    {
      function: "Integral",
      latexSyntax:
        "\\int_{lower limit}^{upper limit} expression \\, d{variable}",
      example: "\\int_0^1 x^2 \\, dx = \\frac{1}{3}",
      view: "∫₀¹ x² dx = 1/3",
    },
    {
      function: "Derivative",
      latexSyntax: "\\frac{d}{dx} (expression)",
      example: "\\frac{d}{dx} x^3 = 3x^2",
      view: "d/dx (x³) = 3x²",
    },
  ];

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here if needed
    });
  };

  return (
    <CommonModal
      isOpen={isOpen}
      onClose={onClose}
      title="Hints on Using The Editor for Formulas"
      wideContent={true}
      size="6xl"
      scrollBehavior="inside"
      closeOnOverlayClick={true}
      closeOnEsc={true}
    >
      <Box px={6} pb={6}>
        <Box mb={4}>
          <Text fontSize="14px" color="#E83831" fontWeight="medium">
            Type a &quot;$&quot; at the <strong>start</strong> and{" "}
            <strong>end</strong> of your equation to format correctly.{" "}
            <Text as="span" color="#3B5A73">
              eg. Equation$
            </Text>
          </Text>
        </Box>

        <Table
          variant="simple"
          size="sm"
          role="table"
          aria-label="LaTeX syntax examples"
        >
          <Thead>
            <Tr backgroundColor="#F7F9FC">
              <Th
                fontSize="12px"
                fontWeight="semibold"
                color="#3B5A73"
                py={3}
                scope="col"
              >
                Function
              </Th>
              <Th
                fontSize="12px"
                fontWeight="semibold"
                color="#3B5A73"
                py={3}
                scope="col"
              >
                LaTeX Syntax
              </Th>
              <Th
                fontSize="12px"
                fontWeight="semibold"
                color="#3B5A73"
                py={3}
                scope="col"
              >
                Example
              </Th>
              <Th
                fontSize="12px"
                fontWeight="semibold"
                color="#3B5A73"
                py={3}
                scope="col"
              >
                View
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {latexExamples.map((item, index) => (
              <Tr key={index} borderBottom="1px solid #E2E8F0">
                <Td fontSize="12px" color="#3B5A73" py={3}>
                  {item.function}
                </Td>
                <Td fontSize="12px" py={3}>
                  <Code
                    fontSize="11px"
                    backgroundColor="#F7F9FC"
                    color="#3B5A73"
                    px={2}
                    py={1}
                    borderRadius="4px"
                    cursor="pointer"
                    _hover={{ backgroundColor: "#E2E8F0" }}
                    onClick={() => handleCopyToClipboard(item.latexSyntax)}
                    title="Click to copy LaTeX syntax"
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        handleCopyToClipboard(item.latexSyntax);
                      }
                    }}
                  >
                    {item.latexSyntax}
                  </Code>
                </Td>
                <Td fontSize="12px" py={3}>
                  <Code
                    fontSize="11px"
                    backgroundColor="#F7F9FC"
                    color="#3B5A73"
                    px={2}
                    py={1}
                    borderRadius="4px"
                    cursor="pointer"
                    _hover={{ backgroundColor: "#E2E8F0" }}
                    onClick={() => handleCopyToClipboard(`$${item.example}$`)}
                    title="Click to copy complete example with $ delimiters"
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        handleCopyToClipboard(`$${item.example}$`);
                      }
                    }}
                  >
                    {item.example}
                  </Code>
                </Td>
                <Td fontSize="12px" color="#3B5A73" py={3}>
                  {item.view}
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>

        <Box mt={4} p={3} backgroundColor="#F7F9FC" borderRadius="6px">
          <Text fontSize="12px" color="#3B5A73" fontStyle="italic">
            💡 <strong>Tip:</strong> Click on any LaTeX syntax or example code
            above to copy it to your clipboard for easy use in the editor.
          </Text>
        </Box>
      </Box>
    </CommonModal>
  );
};

export default LaTeXHintsModal;

"use client";

import ButtonCTA from "@/components/commons/ButtonCTA/ButtonCTA";
import CommonSelect from "@/components/commons/CommonSelect/CommonSelect";
import {
  mockExamCentres,
  mockStateOptions,
  mockExamCenterChangeReasons,
} from "@/utils/commons";
import { Stack, Text, Textarea } from "@chakra-ui/react";
import { useRouter } from "next/dist/client/components/navigation";
import Image from "next/image";
import React, { useState } from "react";
const ChangeExamCenterWrapper = () => {
  const [state, setState] = useState<string | undefined>(
    mockStateOptions[0].value,
  );
  const [examCentre, setExamCentre] = useState<string | undefined>(
    mockExamCentres[0].value,
  );
  const [changeRequestReasons, setChangeRequestReasons] = useState<
    string | undefined
  >(mockExamCenterChangeReasons[0].value);

  const onChangeStateOptions = (value: string) => {
    setState(value);
  };

  const onChangeExamCentre = (value: string) => {
    setExamCentre(value);
  };
  const onChangeChangeRequestReasons = (value: string) => {
    setChangeRequestReasons(value);
  };

  const router = useRouter();
  return (
    <div className="px-8 pt-8">
      <button
        className="flex items-center gap-2 text-base font-medium text-[#0A3150]"
        onClick={() => {
          router.back();
        }}
      >
        <Image
          src={"/images/icons/back.svg"}
          alt="back arrow"
          width={24}
          height={24}
        />
        Back
      </button>
      <div className="px-auto mx-auto mt-[20px] h-full w-[450px]">
        <Text
          fontWeight="bold"
          fontSize="32px"
          lineHeight="40px"
          className="my-4 text-center text-[#0A3150]"
        >
          Request Centre Change
        </Text>
        <Text className="my-2 text-center text-sm font-semibold text-[#0A3150]">
          Current Centre
        </Text>
        <Text
          fontSize="14px"
          fontWeight="semibold"
          className="text-center text-[#5B758A]"
        >
          Miva flagship centre
        </Text>
        <Text
          fontSize="16px"
          fontWeight="medium"
          className="text-center text-[#5B758A]"
        >
          1059 O.P. Fingesi Road, Mabushi, Abuja 900108, Federal Capital
          Territory
        </Text>

        <Stack className="my-4 w-full">
          <Text fontWeight="semibold" fontSize="14px">
            State
          </Text>
          <CommonSelect
            label=""
            placeholder="State"
            value={state}
            showIndicatorIcon={true}
            options={mockStateOptions}
            className="bg-white shadow-none focus:!ring-0"
            onChange={onChangeStateOptions}
          />
        </Stack>

        <Stack className="my-4 w-full">
          <Text fontWeight="semibold" fontSize="14px">
            Exam Centre
          </Text>
          <CommonSelect
            label=""
            placeholder="Exam Centre"
            value={examCentre}
            showIndicatorIcon={true}
            options={mockExamCentres}
            className="bg-white shadow-none focus:!ring-0"
            onChange={onChangeExamCentre}
          />
        </Stack>

        <Stack className="my-4 w-full">
          <Text fontWeight="semibold" fontSize="14px">
            Request Reason
          </Text>
          <CommonSelect
            label=""
            placeholder="Exam Centre"
            value={changeRequestReasons}
            showIndicatorIcon={true}
            options={mockExamCenterChangeReasons}
            className="bg-white shadow-none focus:!ring-0"
            onChange={onChangeChangeRequestReasons}
          />
          {changeRequestReasons?.toLowerCase() === "others" && (
            <Textarea
              className="border-4 text-sm"
              rows={5}
              placeholder="Tell us your reason"
              bg="white"
            ></Textarea>
          )}
        </Stack>

        <ButtonCTA
          onClick={() => {
            router.push("/exam/management");
          }}
          className="mt-8 w-full"
        >
          Save
        </ButtonCTA>
      </div>
    </div>
  );
};

export default ChangeExamCenterWrapper;

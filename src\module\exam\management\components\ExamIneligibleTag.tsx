import { EColor } from "@/constants/colors";
import { Box } from "@chakra-ui/react";

const ExamIneligibleTag = () => {
  return (
    <Box
      borderRadius="8px"
      bg={EColor.PROVINCIAL_PINK}
      className="flex flex-row items-center justify-center gap-2"
      py="8px"
      px="16px"
      color={EColor.RED_COLOR}
      fontWeight="semibold"
      fontSize="14px"
    >
      Ineligible
    </Box>
  );
};

export default ExamIneligibleTag;

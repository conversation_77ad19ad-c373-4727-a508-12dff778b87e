import { Paginated } from "src/api/config/api.d";
import { convertUTCTimeToLocalTimeV2 } from "@/utils/dateUtils";
import { getUpcomingExams } from "@/api/repository/exam-management/get-upcoming-exams";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { isExamLive } from "@/utils/commons";
import { ExamCard } from "@/api/repository/exam-management/exam";

export const useGetUpcomingExams = (page: number, perPage: number) => {
  const query = useQuery({
    queryKey: ["getUpcomingExams"],
    queryFn: () => getUpcomingExams(page, perPage),
    retry: false,
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
    select: (response) => {
      const {
        data,
        currentPage,
        nextPage,
        perPage,
        prevPage,
        total,
        totalPages,
      } = response.data as Paginated<ExamCard[]>;
      const examWithTag = data.map((exam) => ({
        ...exam,
        tags: isExamLive(
          convertUTCTimeToLocalTimeV2(exam.duration_window_end_date as string),
          convertUTCTimeToLocalTimeV2(
            exam.duration_window_start_date as string,
          ),
          exam.is_eligible as boolean,
        ),
      }));
      return {
        examWithTag,
        meta: {
          currentPage,
          nextPage,
          perPage,
          prevPage,
          total,
          totalPages,
        },
      };
    },
  });
  return query;
};

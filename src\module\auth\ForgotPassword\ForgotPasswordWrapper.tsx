"use client";

import { EVIEW_PORT } from "@/constants/enums";
import { TParamPageCommon } from "@/constants/types";
import { AuthContainer } from "../components/AuthContainer";
import RightImageBlock from "../components/RightImageBlock";
import ForgotPasswordContainer from "./ForgotPasswordContainer";
import { useForgotPassword } from "./useForgotPassword";
import ResetPasswordResend from "./ForgotPasswordResend";

const ForgotPasswordWrapper = ({ searchParams }: TParamPageCommon) => {
  const { handleInitiateReset, handleResend, verifyEmail } =
    useForgotPassword();
  return (
    <AuthContainer>
      {verifyEmail ? (
        <ResetPasswordResend email={verifyEmail} handleResend={handleResend} />
      ) : (
        <ForgotPasswordContainer handleInitiateReset={handleInitiateReset} />
      )}
      {searchParams?.viewport !== EVIEW_PORT.MOBILE && <RightImageBlock />}
    </AuthContainer>
  );
};

export default ForgotPasswordWrapper;

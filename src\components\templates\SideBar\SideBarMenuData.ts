import { SideBarMenuItemProps } from "./SideBar.d";
import { EIconName } from "@/components/commons/Icons/Icon.enums";

export const menus: SideBarMenuItemProps[] = [
  {
    title: "Exam",
    to: "",
    iconName: EIconName.DASHBOARD,
    subMenus: [
      {
        title: "All Exams",
        to: "/exam/all-exams",
      },
      {
        title: "Exam Management",
        to: "/exam/exam-management",
      },
      {
        title: "Question Bank",
        to: "/dashboard/enrolment-reports",
      },
    ],
  },
  {
    title: "Results",
    to: "",
    iconName: EIconName.APPLICATION,
    subMenus: [
      {
        title: "Application Analytics",
        to: "/results/application-analytics",
      },
      {
        title: "All Applications",
        to: "/results/all-applications",
      },
    ],
  },
  {
    title: "Exam Centres",
    to: "",
    iconName: EIconName.ENROLLMENTS,
    subMenus: [
      {
        title: "All Centres",
        to: "/exam-centres/all",
      },
    ],
  },
  {
    title: "Users",
    to: "",
    iconName: EIconName.ENROLLMENTS,
    subMenus: [
      {
        title: "All Users",
        to: "/users/all",
      },
    ],
  },
  {
    title: "Settings",
    to: "",
    iconName: EIconName.ENROLLMENTS,
    subMenus: [
      {
        title: "System Settings",
        to: "/settings/system-settings",
      },
    ],
  },
];

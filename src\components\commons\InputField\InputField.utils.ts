import { SelectOption } from "./SelectInput.d";

export const formatUpperIdNameToOptions = (
  data: { ID: string; Name: string }[],
): SelectOption[] => {
  return data.map((item) => ({
    value: item.ID,
    label: item.Name,
  }));
};

export const formatIdNameToOptions = (
  data: { id: string; name: string }[],
): SelectOption[] => {
  return data.map((item) => ({
    value: item.id,
    label: item.name,
  }));
};

import { useE<PERSON>or, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Underline from "@tiptap/extension-underline";
import Strike from "@tiptap/extension-strike";
import Blockquote from "@tiptap/extension-blockquote";
import CodeBlock from "@tiptap/extension-code-block";
import Subscript from "@tiptap/extension-subscript";
import Superscript from "@tiptap/extension-superscript";
import Heading from "@tiptap/extension-heading";
import ListItem from "@tiptap/extension-list-item";
import OrderedList from "@tiptap/extension-ordered-list";
import Link from "@tiptap/extension-link";
import Image from "@tiptap/extension-image";
import Color from "@tiptap/extension-color";
import Table from "@tiptap/extension-table";
import TextAlign from "@tiptap/extension-text-align";
import { MathExtension } from "@aarkue/tiptap-math-extension";
import "katex/dist/katex.min.css";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import CustomTableCell from "@/components/commons/Editor/components/extentions/CustomTableCell";

import "./Editor.scss";
import { convertLatexInDoc } from "./utils";
import { useEffect } from "react";

const CustomMathExtension = MathExtension.extend({
  renderHTML({ node }: { node: any }) {
    return [
      "span",
      {
        class: "math-rendered",
        "data-type": node.attrs.displayMode ? "block-math" : "inline-math",
      },
      `${node.attrs.content}`, // Only render processed content
    ];
  },
});

interface ViewEditorProps {
  value?: string;
}

const ViewEditor = ({ value = "" }: ViewEditorProps) => {
  const editor = useEditor({
    extensions: [
      StarterKit as any,
      Underline,
      Strike,
      Blockquote,
      CodeBlock,
      Subscript,
      Superscript,
      ListItem,
      OrderedList,
      Heading.configure({ levels: [1, 2, 3] }),
      Link.configure({
        openOnClick: false, // Prevents opening link on click, allowing selection
        autolink: true, // Auto-detect links
      }),
      Image,
      Color,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      CustomMathExtension.configure({
        evaluation: true,
        addInlineMath: true,
      }),
      Placeholder.configure({
        placeholder: "Write something...",
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      CustomTableCell,
    ],
    content: value,
    editable: false,
  });

  useEffect(() => {
    if (editor && value) {
      // Set the content as plain text
      editor.commands.setContent(value, false); // false = not HTML
      // Convert LaTeX patterns to math nodes
      convertLatexInDoc(editor);
    }
  }, [editor, value]);

  if (!editor) return <p>Loading editor...</p>;

  return (
    <div className="custom-editor view-editor">
      <EditorContent editor={editor} />
    </div>
  );
};

export default ViewEditor;

import { getCurrentUser } from "@/api/repository/currentUser";
import { createStandaloneToast } from "@chakra-ui/react";
import { redirect } from "next/navigation";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { ILoginVerifyResponse } from "../../module/auth/Login/Login.d";
import { AuthenticationState, AuthUser } from "./authentication.d";

const { toast } = createStandaloneToast();

export const useAuthStore = create<AuthenticationState>()(
  devtools(
    persist(
      (set) => ({
        access_token: null,
        user: undefined,
        login: async (value: ILoginVerifyResponse): Promise<AuthUser> => {
          set({ access_token: value.access_token, user: undefined });
          // Fetch logged in user details
          const user = await getCurrentUser();

          if (!user) {
            return redirect("/login");
          }
          if (!user.role) {
            toast?.({
              title: "Access denied",
              description:
                "You cannot access the admin sis without an admin role",
              status: "error",
            });
            return redirect("/login");
          }
          set((prev) => ({ ...prev, user }));
          return user;
        },
        logout: () => {
          set({ access_token: "", user: undefined });
        },
      }),
      {
        name: "auth-storage",
        getStorage: () => localStorage,
        merge: (persistedState, currentState) => ({
          ...currentState,
          ...(persistedState as AuthenticationState),
        }),
      },
    ),
    {
      name: "authStore",
      enabled: true,
    },
  ),
);

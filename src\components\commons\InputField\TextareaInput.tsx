import { FC } from "react";
import { Textarea, Text } from "@chakra-ui/react";

import { ITextFieldProps } from "./TextareaInput.d";

const TextAreaField: FC<ITextFieldProps> = ({
  label,
  onChange,
  isInvalid = false,
  placeholder = "Input here",
  ...inputProps
}) => {
  return (
    <div>
      {!!label && <Text>{label}</Text>}
      <Textarea
        placeholder={placeholder}
        onChange={onChange}
        isInvalid={isInvalid}
        {...inputProps}
      />
    </div>
  );
};

export default TextAreaField;

import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import {
  GetSingleQuestionRequestType,
  GetSingleQuestionResponseType,
} from "./get-single-question";
import { APIResponse } from "@/api/config/api.d";

export const getSingleQuestionDetails = async ({
  exam_id,
  question_id,
  session_id,
}: GetSingleQuestionRequestType): Promise<
  APIResponse<GetSingleQuestionResponseType>
> => {
  try {
    const response = await baseApi.get(
      `/${Routes[MODULE_ROUTE.STUDENT].QUESTIONS}/${exam_id}/sessions/${session_id}/questions/${question_id}`,
    );
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

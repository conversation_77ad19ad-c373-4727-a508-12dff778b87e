import { baseApi } from "src/api/config/api";
import { MODULE_ROUTE, Routes } from "src/api/config/routes";
import {
  EndExamResponseType,
  ExamQuestionsRequestType,
  StartExamRequestType,
  StartExamResponseType,
  SubmitExamRequestType,
  SubmitExamResponseType,
} from "./ExamQuestions";
import { useQueryClient } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { QUERY_KEY_GET_EXAM_REMAINING_TIME } from "@/hooks/useExamSession/useGetRemainingTime";

const startExam = ({
  exam_id,
}: StartExamRequestType): Promise<AxiosResponse<StartExamResponseType>> => {
  return baseApi.post(
    `/${Routes[MODULE_ROUTE.STUDENT].QUESTIONS}/${exam_id}/sessions`,
  );
};
export const useStartExam = () => {
  return useMutation<
    AxiosResponse<StartExamResponseType>,
    AxiosError,
    StartExamRequestType
  >({
    mutationFn: ({ exam_id }: StartExamRequestType) =>
      startExam({ exam_id: exam_id }),
    onSuccess: () => {},
    onError: () => {},
  });
};

const submitExam = ({
  exam_id,
  session_id,
  answers,
}: SubmitExamRequestType): Promise<AxiosResponse<SubmitExamResponseType>> => {
  return baseApi.post(
    `/${Routes[MODULE_ROUTE.STUDENT].QUESTIONS}/${exam_id}/sessions/${session_id}/submit`,
    { answers },
  );
};
export const useSubmitExam = () => {
  const queryClient = useQueryClient();
  return useMutation<
    AxiosResponse<SubmitExamResponseType>,
    AxiosError,
    SubmitExamRequestType
  >({
    mutationFn: ({ exam_id, session_id, answers }: SubmitExamRequestType) =>
      submitExam({
        exam_id: exam_id,
        session_id: session_id,
        answers: answers,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY_GET_EXAM_REMAINING_TIME],
      });
    },
    onError: () => {},
  });
};
const endExam = ({ exam_id, session_id }: ExamQuestionsRequestType) => {
  return baseApi.post(
    `/${Routes[MODULE_ROUTE.STUDENT].QUESTIONS}/${exam_id}/sessions/${session_id}/end`,
    {},
  );
};
export const useEndExam = () => {
  return useMutation<
    AxiosResponse<EndExamResponseType>,
    AxiosError,
    ExamQuestionsRequestType
  >({
    mutationFn: ({ exam_id, session_id }: ExamQuestionsRequestType) =>
      endExam({
        exam_id: exam_id,
        session_id: session_id,
      }),
    onSuccess: () => {},
    onError: () => {},
  });
};

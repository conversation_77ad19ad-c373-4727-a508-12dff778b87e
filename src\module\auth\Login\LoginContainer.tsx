"use client";
import { FormikBag, withFormik } from "formik";
import validator from "validator";
import LoginForm from "./LoginForm";

import { ILoginContainerProps, ILoginValue } from "./Login.d";
import { checkValueError, validateRequired } from "@/lib/utils/validation";
import { IValidations } from "@/lib/utils/validation.d";

const validateFields: IValidations<ILoginValue> = {
  email: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
    {
      validator: validator.isEmail,
      code: "Email is invalid",
    },
  ],
  password: [
    {
      validator: validateRequired,
      code: "This field is required",
    },
  ],
};

export const onSubmit = async (
  values: ILoginValue,
  {
    setErrors,
    props,
    setSubmitting,
  }: FormikBag<ILoginContainerProps, ILoginValue>,
) => {
  setSubmitting(true);
  try {
    await props.handleLogin(values);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    console.warn(e);
    setErrors(e);
  }
};

const LoginFormContainer = withFormik<ILoginContainerProps, ILoginValue>({
  mapPropsToValues: () => {
    return { email: "", password: "" };
  },
  validate: checkValueError(validateFields),
  handleSubmit: onSubmit,
  validateOnChange: true,
})(LoginForm);

export default LoginFormContainer;

"use client";

import React, { useMemo } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  Image,
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>on,
  MenuList,
  MenuItem,
  But<PERSON>,
} from "@chakra-ui/react";
import { useAuthStore } from "src/store/AuthenticationStore/authentication";
import { getFullName } from "../../../utils/commons";

const Header = () => {
  const router = useRouter();
  const logout = useAuthStore((state) => state.logout);
  const user = useAuthStore((state) => state.user);

  const fullName = useMemo(() => {
    return getFullName(user?.biography.first_name, user?.biography.last_name);
  }, [user]);

  const handleLogout = () => {
    logout();
    router.push("/login");
  };

  return (
    <header className="sticky top-0 z-40 flex h-[62px] items-center justify-end gap-10 border-[#DDE2E7] bg-white px-4 text-[#0A3150] lg:px-6">
      <div className="flex w-full items-center justify-between md:mx-[100px]">
        <Image alt="miva-logo" src="/images/logo.svg" />
        {!!user && (
          <div className="flex">
            <Menu>
              <MenuButton
                background="#ffffff"
                as={Button}
                _hover={{
                  bg: "transparent",
                }}
                _active={{
                  bg: "transparent",
                }}
              >
                <div className="flex cursor-pointer items-center gap-3">
                  <Image alt="Avatar" src="/images/icons/avatar.svg" />
                  <div className="text-sm font-semibold">{fullName}</div>
                  <Image
                    alt="chevron-down"
                    src="/images/icons/chevron-down.svg"
                  />
                </div>
              </MenuButton>
              <MenuList>
                <MenuItem
                  onClick={() => handleLogout()}
                  fontSize={"14px"}
                  fontWeight={600}
                >
                  Logout
                </MenuItem>
              </MenuList>
            </Menu>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;

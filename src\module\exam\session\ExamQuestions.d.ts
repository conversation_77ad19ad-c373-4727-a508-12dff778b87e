export interface ExamQuestionsRequestType {
  exam_id: string;
  session_id: string;
  page?: number;
  limit?: number;
}
export interface ExamQuestionsRemainitimeRequestType {
  exam_id: string;
}
export interface StartExamRequestType {
  exam_id: string;
}
export interface ExamQuestionResponseType {
  status: string;
  message: string;
  data: QuestionResponse;
}

export interface QuestionResponse {
  currentPage: number;
  data: SingleQuestionType[];
  nextPage: number;
  perPage: number;
  prevPage: number;
  total: number;
  totalPages: number;
}

export interface SingleQuestionType {
  created_at: string;
  id: string;
  options: Option[];
  points: number;
  question: string;
  question_type: string;
  updated_at: string;
  word_count: number;
}

export interface Option {
  id: string;
  value: string;
}
export interface StartExamResponseType {
  status: string;
  message: string;
  data: string;
}

export interface SubmitExamResponseType {
  data: string;
  errors: string;
  message: string;
  status: string;
}

export interface SubmitExamRequestType {
  exam_id: string;
  session_id: string;
  answers: Answer[];
}
export interface Answer {
  answer_text?: string | undefined;
  option_id?: string | undefined;
  question_id: string | undefined;
}
export interface EndExamResponseType {
  data: string;
  message: string;
  status: string;
}

export interface GetSingleQuestionRequestType {
  exam_id: string;
  session_id: string;
  question_id: string;
}

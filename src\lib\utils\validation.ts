import { FormikValues } from "formik";
import { get, keys, has, isEmpty } from "lodash";
import { IValidations } from "./validation.d";

export const emailFormat =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

// eslint-disable-next-line no-control-regex
export const notUnicodeFormat = /[^\u0000-\u00ff]/;
export const validateEmail = (email: string) => {
  if (typeof email === "string") {
    const lowerCase = String(email).toLowerCase();

    if (!emailFormat.test(lowerCase) || notUnicodeFormat.test(lowerCase)) {
      return false;
    }
  }

  return true;
};
export const validateRequired = (value: string | number) => {
  let error = true;

  // If the input field is empty, the value will be assigned to NaN. So, the type of the "NaN" is a number,
  // but it's not a number type when the field is empty. So we need to check it's actually a number type.
  if (typeof value === "number" && !isNaN(value)) {
    return true;
  }

  if (isEmpty(value) && isEmpty(value && value.toString())) {
    error = false;
  }
  return error;
};
export const validateMin = (min: number) => (value: string | number) => {
  return value == "" || Number(value) >= min;
};
export const validateMax = (max: number) => (value: string | number) => {
  return Number(value) <= max;
};

// Custom validator to check if a number is greater than 1
export const validateGreaterThanOne = (value: string | number) => {
  if (typeof value === "number") {
    return value > 0;
  }
  if (typeof value === "string" && !isNaN(Number(value))) {
    return parseFloat(value) > 0;
  }
  return false;
};

export const checkValueError =
  <T>(validations: IValidations<T>) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (values: FormikValues, props?: any) => {
    const error: { [Property in keyof T]?: string } = {};
    let checkValidate = false;
    keys(validations).forEach((path: string) => {
      const pathValue = get(values, path);
      const isExistingKey = has(values, path);
      if (!isExistingKey) {
        // tslint:disable-next-line:no-console
        console &&
          console.error(`The field ${path} does not existing on the form`);
      }
      for (let i = 0; i < validations[path as keyof T].length; i += 1) {
        const pathItem = validations[path as keyof T][i] ?? {};
        if (typeof pathItem.validator === "function")
          checkValidate = pathItem.validator(pathValue, values, props);
        if (!checkValidate) {
          const { code } = pathItem;
          error[path as keyof T] = code;
          return error;
        }
      }
    });

    return error;
  };

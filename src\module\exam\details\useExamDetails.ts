import { extractAxiosError } from "@/lib/utils/helpers";
import { useToast } from "@chakra-ui/react";
import axios from "axios";
import { SetStateAction } from "react";
import { baseApi } from "src/api/config/api";
import { APIResponse } from "src/api/config/api.d";
import { MODULE_ROUTE, ManagementRoutes } from "src/api/config/routes";
import { ExamCard } from "../management/ExamManagement.d";
import { convertUTCTimeToLocalTimeV2 } from "@/utils/dateUtils";

//import { getUserByAccessToken } from "../CAS/cas.utils";
export const useExamDetails = () => {
  const toast = useToast();

  const handleExamDetailsFetch = async (
    id: string,
    setExamDetails: React.Dispatch<SetStateAction<ExamCard>>,
    stopLoading: VoidFunction,
  ) => {
    try {
      const res = await baseApi.get<APIResponse<ExamCard>>(
        `${ManagementRoutes[MODULE_ROUTE.STUDENT].DETAILS}${id}`,
      );
      if (res.data.status === "OK") {
        const exam = res.data.data;
        setExamDetails({
          ...exam,
          duration_window_end_date: convertUTCTimeToLocalTimeV2(
            exam.duration_window_end_date as string,
          ),
          duration_window_start_date: convertUTCTimeToLocalTimeV2(
            exam.duration_window_start_date as string,
          ),
        });
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        toast({
          description: extractAxiosError(error),
          status: "error",
        });
      }
      console.warn(error);
    } finally {
      stopLoading();
    }
  };
  return { handleExamDetailsFetch };
};

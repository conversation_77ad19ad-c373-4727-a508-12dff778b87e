import { AuthUser } from "src/store/AuthenticationStore/authentication.d";
import { baseApi } from "../config/api";
import { APIResponse } from "../config/api.d";
import { MODULE_ROUTE, Routes } from "../config/routes";

export const getCurrentUser = async (): Promise<AuthUser> => {
  const user = (
    await baseApi.get<APIResponse<AuthUser>>(Routes[MODULE_ROUTE.AUTH].AUTH_ME)
  ).data.data;

  return user;
};

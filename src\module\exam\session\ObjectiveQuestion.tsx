"use client";

import { VStack } from "@chakra-ui/react";
import React from "react";
import { ObjectiveOption } from "./ObjectiveOptions";

type ObjectiveOptionsProps = {
  options: string[];
  selectedOption: number | undefined;
  onSelectOption: (value: number) => void;
  exam_id: string;
  session_id: string;
  question_id: string;
};

export const ObjectiveOptions = ({
  options,
  selectedOption,
  onSelectOption,
}: ObjectiveOptionsProps) => {
  const optionHeaders = ["A", "B", "C", "D", "E", "F"];

  return (
    <VStack gap="10px" width="100%" mb="64px" className="p-8">
      {options.map((option, index) => {
        const letter = optionHeaders[index] || String(index + 1);
        return (
          <ObjectiveOption
            key={index}
            letter={letter}
            value={option}
            selected={selectedOption === index}
            onClick={() => onSelectOption(index)}
          />
        );
      })}
    </VStack>
  );
};

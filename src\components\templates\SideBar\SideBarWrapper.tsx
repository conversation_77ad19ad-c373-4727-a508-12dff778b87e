import { menus } from "@/components/templates/SideBar/SideBarMenuData";
import { SideBarMenuItem } from "@/components/templates/SideBar/SideBarMenuItem";
import { useSideBarStore } from "../../../store/SideBarStore/sideBar";
import { cn } from "@/lib/utils";
import { SubMenu } from "./SideBar";
import { Image } from "@chakra-ui/react";

import "@/styles/sidebar.scss";

const SideBarWrapper = () => {
  const isExpand = useSideBarStore((state) => state.isExpandSideBar);

  return (
    <div
      className={cn(
        "sidebar-menu-wrapper bg-primary-900 scrollbar-none sticky right-0 top-0 h-[100vh] max-h-[100vh] w-[240px] overflow-y-scroll py-[12px]",
        {
          "max-w-[240px] duration-300 ease-in-out": isExpand,
          "max-w-[80px] duration-300 ease-in-out": !isExpand,
        },
      )}
    >
      <div className="px-[12px]">
        {isExpand ? (
          <Image
            className="logo-img mb-[24px] object-contain"
            alt="Logo"
            src="/images/logo-miva.png"
            width={240}
            height={50}
          />
        ) : (
          <Image
            className="logo-img mb-[24px] object-contain"
            alt="Logo"
            src="/images/mini-logo.png"
            height={50}
          />
        )}
      </div>
      {menus.map(({ subMenus, ...menu }, menuIndex) => {
        return (
          <SideBarMenuItem
            index={`${menuIndex}`}
            key={`${menu.title}-${menuIndex}`}
            {...menu}
            subMenus={subMenus}
          >
            {Array.isArray(subMenus) && subMenus.length > 0 && isExpand
              ? subMenus.map((subMenu: SubMenu, subMenuIndex: number) => {
                  return (
                    <SideBarMenuItem
                      key={`${subMenu.title}-${subMenuIndex}`}
                      index={`${menuIndex}-${subMenuIndex}`}
                      {...subMenu}
                    />
                  );
                })
              : null}
          </SideBarMenuItem>
        );
      })}
    </div>
  );
};

export default SideBarWrapper;

const LinkPopover = ({
  show,
  url,
  onUrlChange,
  onSubmit,
  onCancel,
}: {
  show: boolean;
  url: string;
  onUrlChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
  onCancel: () => void;
}) => {
  if (!show) return null;

  return (
    <div className="absolute z-10 mt-2 rounded border bg-white p-4 shadow-lg">
      <div className="space-y-2">
        <input
          type="text"
          value={url}
          onChange={onUrlChange}
          placeholder="Enter URL"
          className="w-full rounded border p-2"
        />
        <div className="flex justify-end gap-2">
          <button
            onClick={onCancel}
            className="rounded border px-3 py-1 text-sm hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            onClick={onSubmit}
            className="rounded bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600"
          >
            Apply
          </button>
        </div>
      </div>
    </div>
  );
};

export default LinkPopover;
